/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.multifunding.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.springblade.common.enums.*;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.Condition;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.dto.GoodsSearchDTO;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.service.ICustomerGoodsInfoService;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.customer.vo.CustomerGoodsVO;
import org.springblade.multifunding.constant.MultiFundingProcessEnum;
import org.springblade.multifunding.constant.NeedCalculateEnum;
import org.springblade.multifunding.dto.ProductTemplateDTO;
import org.springblade.multifunding.dto.multiFundingProductSaveAndUpdateDTO;
import org.springblade.multifunding.entity.MultiFundingAffiliatedProducts;
import org.springblade.multifunding.entity.MultiFundingProduct;
import org.springblade.multifunding.mapper.MultiFundingProductMapper;
import org.springblade.multifunding.service.IMultiFundingAffiliatedProductsService;
import org.springblade.multifunding.service.IMultiFundingProductProcessService;
import org.springblade.multifunding.service.IMultiFundingProductService;
import org.springblade.multifunding.service.IMultiFundingRiskProductService;
import org.springblade.multifunding.vo.MultiFundingProductProcessVO;
import org.springblade.multifunding.vo.MultiFundingProductVO;
import org.springblade.multifunding.vo.MultiGoodsContractTemplateVO;
import org.springblade.multifunding.vo.MyMultiFundingProductVO;
import org.springblade.multifunding.wrapper.MultiFundingProductWrapper;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.product.common.constant.GoodsTypeBusinessEnum;
import org.springblade.product.common.constant.GoodsTypeCodeEnum;
import org.springblade.product.common.dto.GoodsLabelRelationDTO;
import org.springblade.product.common.dto.ProductDTO;
import org.springblade.product.common.entity.GoodsType;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.entity.ProductManager;
import org.springblade.product.common.vo.GoodsContractTemplateVO;
import org.springblade.product.common.vo.GoodsLabelRelationVO;
import org.springblade.product.common.vo.ProductVO;
import org.springblade.product.goods_contract.service.IGoodsContractTemplateService;
import org.springblade.product.moudle.goodstype.service.IGoodsTypeService;
import org.springblade.product.moudle.pubproduct.service.IProductConfigInfoHandler;
import org.springblade.product.moudle.pubproduct.service.IProductManagerService;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.system.utils.UserUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 多资方产品表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Service
@RequiredArgsConstructor
public class MultiFundingProductServiceImpl extends BaseServiceImpl<MultiFundingProductMapper, MultiFundingProduct> implements IMultiFundingProductService {

	private final ProductDirector productDirector;

	private final IProductManagerService productManagerService;

	private final ICustomerGoodsService customerGoodsService;

	private final IGoodsContractTemplateService goodsContractTemplateService;

	private final IEnterpriseQuotaService enterpriseQuotaService;

	private final IMultiFundingAffiliatedProductsService multiFundingAffiliatedProductsService;

	private final IMultiFundingProductProcessService multiFundingProductProcessService;

	private final IBusinessProcessProgressService businessProcessProgressService;

	private final List<IProductConfigInfoHandler> productConfigInfoHandlers;

	private final ICustomerGoodsInfoService customerGoodsInfoService;

	private final IGoodsTypeService goodsTypeService;

/*	private final static List<Integer> NEED_CONFIG = Arrays.asList(GoodsEnum.PRODUCT_CONFIG_QUESTION.getCode(), GoodsEnum.PRODUCT_CONFIG_OPENING_PROCESS.getCode()
			, GoodsEnum.PRODUCT_CONFIG_LABEL_RELATION.getCode(), GoodsEnum.PRODUCT_CONFIG_MATERIAL.getCode());*/
private final static List<Integer> NEED_CONFIG = Arrays.asList(GoodsEnum.PRODUCT_CONFIG_QUESTION.getCode(), GoodsEnum.PRODUCT_CONFIG_OPENING_PROCESS.getCode()
		, GoodsEnum.PRODUCT_CONFIG_LABEL_RELATION.getCode(), GoodsEnum.PRODUCT_CONFIG_MATERIAL.getCode()
		, GoodsEnum.PRODUCT_CONFIG_PROCESS.getCode());

	/**
	 * 获取合同模板列表
	 * 目前获取合同模板只用到了五条数据:
	 * goodsId 产品id
	 * sigUser 上传用户
	 * processType 进度类型(2 : 申请额度)
	 * bizNos 业务id(流程进度表的主键id)
	 * sinNode 上传节点
	 * @param productTemplateDTO
	 * @return
	 */
	@Override
	public List<MultiGoodsContractTemplateVO> getAvailableProductTemplate(ProductTemplateDTO productTemplateDTO) {
		// 是否需要更新
		Integer needUpdate = productTemplateDTO.getNeedUpdate();
		// 流程类型
		String processType = productTemplateDTO.getProcessType();
		// 融资id
		Long financeApplyId = productTemplateDTO.getFinanceApplyId();
		// 签署节点
		String signNode = productTemplateDTO.getSignNode();
		// 签署用户
		Integer signUser = productTemplateDTO.getSignUser();
		// 合同ids
		String contractIds = productTemplateDTO.getContractIds();
		// 大业务编号列表
		String bigBizNos = productTemplateDTO.getBigBizNos();

		// map<资金产品id, 业务编号>
		Map<Long, String> goodsIdAndBizNos = productTemplateDTO.getGoodsIdAndBizNosMap();

		// 资金产品id列表
		List<Long> groupIdList = new ArrayList<>(goodsIdAndBizNos.keySet());

		// 初始化返回对象
		List<MultiGoodsContractTemplateVO> multiGoodsContractTemplateVOList = new ArrayList<>();

		// 执行单个产品查询合同模板
		groupIdList.forEach(goodsId -> {
			boolean update = true;
			if (ObjectUtil.isNotEmpty(needUpdate) && needUpdate == 0) {
				update = false;
			}
			// 调用原有方法 查询合同模板
			List<GoodsContractTemplateVO> goodsContractTemplateVOList = goodsContractTemplateService.selectGoodsContractTemplateAndContractStatusListByGoodsId(goodsId, signNode, processType, financeApplyId,
					signUser, update, contractIds, goodsIdAndBizNos.get(goodsId), bigBizNos);

			ProductVO product = productDirector.detail(goodsId);
			MultiGoodsContractTemplateVO templateVO = MultiGoodsContractTemplateVO.builder()
					// 设置合同模板
					.goodsContractTemplateVOList(goodsContractTemplateVOList)
					// 设置资方logo
					.capitalLogo(product.getCapitalLogo())
					// 设置资金方名称
					.capitalName(product.getCapitalName())
					// 设置产品名称
					.goodsName(product.getGoodsName())
					// 产品id
					.goodsId(goodsId)
					.build();
			// 返回
			multiGoodsContractTemplateVOList.add(templateVO);
		});
		return multiGoodsContractTemplateVOList;
	}

	/**
	 * 组装数据
	 * @param goodsId 产品id
	 * @param enterpriseQuotaId 额度id
	 * @param myMultiFundingProductVO 客户产品信息
	 * @param enterpriseQuotaMap 额度信息
	 * @param productMap 产品信息
	 * @param labelMap 标签信息
	 */
	@Override
	public void assemblyData(Long goodsId, Long enterpriseQuotaId, MyMultiFundingProductVO myMultiFundingProductVO,
							  Map<Long, EnterpriseQuota> enterpriseQuotaMap, Map<Long, Product> productMap, Map<Long, List<GoodsLabelRelationVO>> labelMap) {
		// 组装标签
		if (ObjectUtil.isNotEmpty(labelMap)) {
			List<GoodsLabelRelationVO> labelList = labelMap.get(goodsId);
			if (labelList != null) {
				List<Map<String, Object>> labelMaps = labelList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
				myMultiFundingProductVO.setLabelList(labelMaps);
			}
		}
		// 组装额度
		if (ObjectUtil.isNotEmpty(enterpriseQuotaMap)) {
			buildEnterpriseQuotaData(myMultiFundingProductVO, enterpriseQuotaMap.get(enterpriseQuotaId));
		}
		// 组装产品
		if (ObjectUtil.isNotEmpty(productMap)) {
			buildGoodsData(myMultiFundingProductVO, productMap.get(goodsId));
		}
        //组装类型名称
        if (goodsTypeService.isRedemption(productMap.get(goodsId).getGoodsTypeId())) {
            myMultiFundingProductVO.setIndustryFinancingType(GoodsTypeCodeEnum.PRODUCT_GROUP_IS_REDEMPTION.getDesc());
        } else {
            myMultiFundingProductVO.setIndustryFinancingType(GoodsTypeCodeEnum.PRODUCT_GROUP_NOT_REDEMPTION.getDesc());
        }
    }

	/**
	 * 组装额度
	 * @param myMultiFundingProductVO
	 * @param enterpriseQuota
	 */
	private void buildEnterpriseQuotaData(MyMultiFundingProductVO myMultiFundingProductVO, EnterpriseQuota enterpriseQuota) {
		if (Objects.nonNull(enterpriseQuota)) {
			// 可用额度
			myMultiFundingProductVO.setAvailableCredit(enterpriseQuota.getAvailableAmount());
			// 年利率
			myMultiFundingProductVO.setAnnualInterestRate(enterpriseQuota.getAnnualInterestRate());
			// 日利率
			myMultiFundingProductVO.setDailyInterestRate(enterpriseQuota.getDailyInterestRate());
			// 禁用原因
			myMultiFundingProductVO.setDisableReason(enterpriseQuota.getDisableReason());
			// 过期时间
			myMultiFundingProductVO.setExpireTime(enterpriseQuota.getExpireTime());
			//保证金比例
			myMultiFundingProductVO.setBondProportion(enterpriseQuota.getBondProportion());
			//融资占比
			myMultiFundingProductVO.setFinancingProportion(enterpriseQuota.getFinancingProportion());
		}
	}

	/**
	 * 组装产品
	 * @param myMultiFundingProductVO
	 * @param product
	 */
	private void buildGoodsData(MyMultiFundingProductVO myMultiFundingProductVO, Product product) {
		if (ObjectUtil.isNotEmpty(product)) {
			// 借款期限
			myMultiFundingProductVO.setLoadTermEnd(product.getLoadTermEnd());
			// 借款期限单位
			myMultiFundingProductVO.setLoadTermUnit(product.getLoadTermUnit());
			// 借款金额
			myMultiFundingProductVO.setLoanAmountEnd(product.getLoanAmountEnd());
			// 利率类型
			myMultiFundingProductVO.setAnnualInterestRateType(product.getAnnualInterestRateType());
			// 收费方式
			myMultiFundingProductVO.setChargeMethod(product.getChargeMethod());
			// 放款方式
			myMultiFundingProductVO.setLendingMethod(product.getLendingMethod());
			if (myMultiFundingProductVO.getStatus().equals(CustomerGoodsEnum.FAILURE.getCode()) ||
					myMultiFundingProductVO.getStatus().equals(CustomerGoodsEnum.QUOTA_APPLICATION.getCode())) {
				// 产品最低年利率
				myMultiFundingProductVO.setAnnualInterestRateStart(product.getAnnualInterestRateStart());
			}
		}
	}

	/**
	 * 查看我的产品-融资产品列表
	 * @param status 状态
	 * @param enterpriseType 企业类型
	 * @param userId 用户id
	 * @return List<MyMultiFundingProductVO>
	 */
	@Override
	public List<MyMultiFundingProductVO> getMyMultiFundingProduct(String status, Integer enterpriseType, Long userId) {
		IMultiFundingRiskProductService multiFundingRiskProductService = SpringUtil.getBean(IMultiFundingRiskProductService.class);
		// 获取融资产品列表 我的产品-客户产品
		List<CustomerGoods> customerGoodsList = customerGoodsService.getCustomerGoodsList(status, enterpriseType, userId);
		// 调原有的接口 展示单产品信息
		List<CustomerGoodsVO> customerGoodsVOList = customerGoodsInfoService.selectListByStatus(status, enterpriseType, userId);
		if (CollectionUtils.isEmpty(customerGoodsList) && CollectionUtils.isEmpty(customerGoodsVOList)) {
			return Collections.emptyList();
		}

		// 遍历
		List<MyMultiFundingProductVO> myMultiFundingProductVOList =new ArrayList<>();
		customerGoodsList.forEach(customerGoods -> {
			MyMultiFundingProductVO myMultiFundingProductVO = BeanUtil.copyProperties(customerGoods, MyMultiFundingProductVO.class);
			// 融资产品id
			Long groupId = myMultiFundingProductVO.getGroupId();
			// 融资产品关联的 资金产品列表
			List<MultiFundingAffiliatedProducts> affiliatedProducts = multiFundingAffiliatedProductsService.getAffiliatedProductIdListByGroupId(groupId);
			if (CollUtil.isEmpty(affiliatedProducts)){
				return;
			}
			// 资金产品id列表
//			List<Long> goodsIdList = CollStreamUtil.toList(affiliatedProducts, MultiFundingAffiliatedProducts::getGoodsId);
//			List<Long> canOpenGoodsIdList = multiFundingRiskProductService.probeAndScreening(goodsIdList, groupId, userId, enterpriseType);
			List<Long> relatedGoodsIds = CollStreamUtil.toList(affiliatedProducts, MultiFundingAffiliatedProducts::getGoodsId);
			List<CustomerGoods> cGoodsList = customerGoodsService.list(Wrappers.<CustomerGoods>lambdaQuery()
					.eq(CustomerGoods::getEnterpriseId, userId)
					.in(CustomerGoods::getGoodsId, relatedGoodsIds));
			List<Long> canOpenGoodsIdList = CollStreamUtil.toList(cGoodsList, CustomerGoods::getGoodsId);
			// 资金产品 对应 额度信息列表
			BigDecimal totalAvailable =BigDecimal.ZERO;
			if(CollUtil.isNotEmpty(cGoodsList)){
				List<EnterpriseQuota> enterpriseQuotaList = enterpriseQuotaService.getListByGoodsIdsAndEnterpriseTypeAndgEnterpriseId(canOpenGoodsIdList, enterpriseType, userId);
				totalAvailable = enterpriseQuotaList.stream()
						.map(EnterpriseQuota::getAvailableAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			// 获取总可用额度数据
/*			BigDecimal totalAvailable = enterpriseQuotaList.stream()
					.map(EnterpriseQuota::getAvailableAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);*/
			// 设置总可用额度
			myMultiFundingProductVO.setTotalAvailable(totalAvailable);
			// 如果可用额度不为零
			if (!BigDecimal.ZERO.equals(totalAvailable)) {
				// 设置状态
				myMultiFundingProductVO.setStatus(CustomerGoodsEnum.FINANCING.getCode());
				// 检查融资产品 并更新 todo 改造
				checksAndUpdate(groupId, userId);
			}
			// 检查流程是否已提交
			if (checksIsCompleted(groupId)) {
				myMultiFundingProductVO.setIsCompleted(true);
			}
			MultiFundingProduct multiFundingProduct = this.getById(groupId);
			//检查融资产品分类
			if (ObjectUtil.isNotEmpty(multiFundingProduct) && GoodsEnum.PRODUCT_GROUP_ROUTE.getCode().equals(customerGoods.getGoodsType())) {
				Boolean isRedemption= goodsTypeService.isRedemption(multiFundingProduct.getGoodsTypeId());
				if (isRedemption){
					myMultiFundingProductVO.setIndustryFinancingType(GoodsTypeCodeEnum.PRODUCT_GROUP_IS_REDEMPTION.getDesc());
				}else {
					myMultiFundingProductVO.setIndustryFinancingType(GoodsTypeCodeEnum.PRODUCT_GROUP_NOT_REDEMPTION.getDesc());
				}
			}
			myMultiFundingProductVOList.add(myMultiFundingProductVO);
		});
		// 对原有-我的产品接口 进行数据处理 移除订单融资单产品And产品组产品
		customerGoodsVOList.removeIf(customerGoodsVO -> GoodsEnum.ORDER_FINANCING.getCode().equals(customerGoodsVO.getGoodsType()) || ObjectUtil.isNotEmpty(customerGoodsVO.getGroupId()));
		// 根据goodsId对customerGoodsVOList进行去重处理
		customerGoodsVOList = new ArrayList<>(customerGoodsVOList.stream()
				.collect(Collectors.toMap(CustomerGoods::getGoodsId, item -> item, (existing, replacement) -> existing))
				.values());
		myMultiFundingProductVOList.addAll(BeanUtil.copyToList(customerGoodsVOList, MyMultiFundingProductVO.class));
		return myMultiFundingProductVOList;
	}

	/**
	 * 检查流程是否已完成提交审批
	 * @return
	 */
	public Boolean checksIsCompleted(Long businessId) {
		// 流程类型 申请额度
		Integer type = ProcessTypeEnum.APPLY_QUOTA.getCode();
		// 查询融资产品流程进度
		MultiFundingProductProcessVO multiFundingProductProcessStatus = multiFundingProductProcessService.getMultiFundingProductProcessStatus(businessId, type);
		// 存在 且 状态为已完成
		return ObjectUtil.isNotEmpty(multiFundingProductProcessStatus) && Objects.equals(ProcessStatusEnum.FINISH.getCode(), multiFundingProductProcessStatus.getStatus());
	}

	/**
	 * 更新客户产品状态
	 * @param groupId
	 * @param userId
	 */
	public void checksAndUpdate(Long groupId, Long userId) {
		CustomerGoods byGoodsIdAndCustomerId = customerGoodsService.getByGoodsIdAndCustomerId(groupId, userId);
		if (ObjectUtil.isNotEmpty(byGoodsIdAndCustomerId) && !Objects.equals(byGoodsIdAndCustomerId.getStatus(), CustomerGoodsEnum.FINANCING.getCode())) {
			// 更新融资产品 客户产品状态
			customerGoodsService.updateMultiFundingProductStatus(groupId, GoodsEnum.PRODUCT_GROUP_ROUTE.getCode(), UserUtils.getEnterpriseType(), userId, CustomerGoodsEnum.FINANCING.getCode());
		}
	}

	/**
	 * 产品组查看详情 获取关联列表
	 * @param groupId
	 * @return
	 */
	@Override
	public List<MyMultiFundingProductVO> getMyMultiFundingProductDetail(Long groupId) {
		IMultiFundingRiskProductService multiFundingRiskProductService = SpringUtil.getBean(IMultiFundingRiskProductService.class);
		// 用户id
		Long userId = MyAuthUtil.getUserId();
		// 企业类型
		Integer enterpriseType = UserUtils.getEnterpriseType();
		// 获取关联产品列表
		List<MultiFundingAffiliatedProducts> affiliatedProducts = multiFundingAffiliatedProductsService.getAffiliatedProductIdListByGroupId(groupId);
		// 资金产品id列表
//		List<Long> goodsIdList = CollStreamUtil.toList(affiliatedProducts, MultiFundingAffiliatedProducts::getGoodsId);

		// 有效的关联产品列表
//		List<Long> efficiently = multiFundingRiskProductService.probeAndScreening(goodsIdList, groupId, userId, enterpriseType);

		// 查看详情-融资产品  关联资金产品中已开通的产品
//		List<CustomerGoods> customerGoodsList = customerGoodsService.getListByEnterpriseIdAndEnterpriseTypeAndGroupIdAndGoodsIds(userId, enterpriseType, groupId, efficiently);
		List<Long> relatedGoodsIds = CollStreamUtil.toList(affiliatedProducts, MultiFundingAffiliatedProducts::getGoodsId);
		List<CustomerGoods> customerGoodsList = customerGoodsService.list(Wrappers.<CustomerGoods>lambdaQuery()
				.eq(CustomerGoods::getEnterpriseId, userId)
				.in(CustomerGoods::getGoodsId, relatedGoodsIds));

		// 已开通的产品id列表
		List<Long> customerGoodsIdList = CollStreamUtil.toList(customerGoodsList, CustomerGoods::getGoodsId);
		// 不含客户开通的产品id列表
		List<Long> unAvailableProductsIdList = StreamUtil.filter(relatedGoodsIds, e -> !customerGoodsIdList.contains(e));
		// map<id，产品信息>
		Map<Long, Product> productMap = StreamUtil.toMap(productDirector.selectList(relatedGoodsIds), Product::getId, e -> e);
		// map<id， 标签列表>
		Map<Long, List<GoodsLabelRelationVO>> labelMap = getMapLabelInGoodsId(relatedGoodsIds);
		// 额度id
		List<Long> enterpriseQuotaIds = StreamUtil.map(customerGoodsList, CustomerGoods::getEnterpriseQuotaId);
		// map<id， 额度信息>
		Map<Long, EnterpriseQuota> enterpriseQuotaMap = enterpriseQuotaService.getMapInId(enterpriseQuotaIds);

		// 产品列表
		List<CustomerGoodsVO> collect = new ArrayList<>();
		if (CollUtil.isNotEmpty(customerGoodsList)) {
			customerGoodsList.forEach(customerGoods -> {
				collect.add(BeanUtil.copyProperties(customerGoods, CustomerGoodsVO.class));
			});
		}
		// 还未开通的产品列表
		if (CollUtil.isNotEmpty(unAvailableProductsIdList)) {
			// 客户存在未开通的产品列表 进行查询
			List<Product> productList = productDirector.selectList(unAvailableProductsIdList);
			if (CollUtil.isNotEmpty(productList)) {
				productList.forEach(product -> {
					CustomerGoodsVO customerGoodsVO = BeanUtil.copyProperties(product, CustomerGoodsVO.class);
					customerGoodsVO.setGoodsId(product.getId());
					customerGoodsVO.setGoodsType(product.getType());
					customerGoodsVO.setStatus(CustomerGoodsEnum.QUOTA_APPLICATION.getCode());
					collect.add(customerGoodsVO);
				});
			}
		}

		// 组装数据
		List<MyMultiFundingProductVO> map = StreamUtil.map(collect, customerGoods -> {
			MyMultiFundingProductVO myMultiFundingProductVO = BeanUtil.copyProperties(customerGoods, MyMultiFundingProductVO.class);
			// 产品id
			Long goodsId = customerGoods.getGoodsId();
			// 设置流程状态
			settingStatus(myMultiFundingProductVO, goodsId, userId);
			// 组装数据
			assemblyData(goodsId, customerGoods.getEnterpriseQuotaId(), myMultiFundingProductVO, enterpriseQuotaMap, productMap, labelMap);
			return myMultiFundingProductVO;
		});
		return StreamUtil.filter(map, ObjectUtil::isNotEmpty);
	}

	void settingStatus(MyMultiFundingProductVO myMultiFundingProductVO, Long goodsId, Long userId) {
		// 目前只考虑 审批中、已驳回
		final List<Integer> status = Arrays.asList(ProcessStatusEnum.APPROVING.getCode(), ProcessStatusEnum.REJECT.getCode());
		// 获取开通流程进度 申请额度
		BusinessProcessProgress applyQuota = businessProcessProgressService.getByBusinessIdAndType(goodsId, ProcessTypeEnum.APPLY_QUOTA.getCode(), userId);
		// 开通流程记录存在并且
		if (ObjectUtil.isNotEmpty(applyQuota) && status.contains(applyQuota.getStatus())) {
			myMultiFundingProductVO.setProcessStatus(applyQuota.getStatus());
			return;
		}
		// 获取激活额度开通流程
		BusinessProcessProgress quotaActive = businessProcessProgressService.getByBusinessIdAndType(goodsId, ProcessTypeEnum.QUOTA_ACTIVE.getCode(), userId);
		// 开通流程记录存在并且
		if (ObjectUtil.isNotEmpty(quotaActive) && status.contains(quotaActive.getStatus())) {
			myMultiFundingProductVO.setProcessStatus(quotaActive.getStatus());
		}
	}

	/**
	 * 查看详情
	 * @param id 融资产品id
	 * @return multiFundingProductSaveAndUpdateDTO
	 */
	@Override
	public multiFundingProductSaveAndUpdateDTO detail(Long id) {
		// 获取融资产品信息
		MultiFundingProduct product = getById(id);
		// 获取租户id
		final String tenantId = UserUtils.getTenantId();
		ProductVO productVO = BeanUtil.copyProperties(product, ProductVO.class);
		// 产品配置信息
		productConfigInfoHandlers.parallelStream().forEach(e -> {
			if (NEED_CONFIG.contains(e.support())) {
				TenantBroker.runAs(tenantId, a -> e.assembleProduct(productVO));
			}
		});
		multiFundingProductSaveAndUpdateDTO multiFundingProductSaveAndUpdateDTO = BeanUtil.copyProperties(productVO, multiFundingProductSaveAndUpdateDTO.class);
		multiFundingProductSaveAndUpdateDTO.setNeedAdjust(product.getNeedAdjust());
		multiFundingProductSaveAndUpdateDTO.setNeedCalculate(product.getNeedCalculate());
		multiFundingProductSaveAndUpdateDTO.setIsRedemption(goodsTypeService.isRedemption(product.getGoodsTypeId()));
		return multiFundingProductSaveAndUpdateDTO;
	}

	/**
	 * 修改融资产品信息
	 * @param multiFundingProduct
	 * @return
	 */
	@Override
	public Boolean updateProduct(multiFundingProductSaveAndUpdateDTO multiFundingProduct) {
		// 检查是否关联产品，分类是否改变
		MultiFundingProduct product = baseMapper.selectById(multiFundingProduct.getId());
		if (ObjectUtil.isEmpty(product)) {
			throw new ServiceException("未查询到该产品信息");
		}
		Integer relationNum = product.getRelationNum();
		if (relationNum != null && relationNum > 0 && !Objects.equals(product.getGoodsTypeId(), multiFundingProduct.getGoodsTypeId())){
			throw new ServiceException("已关联产品，不允许修改分类");
		}
		//检查是否允许融资调整和多资方融资计算
		Boolean redemption = goodsTypeService.isRedemption(multiFundingProduct.getGoodsTypeId());
		if (redemption) {
			if (1 == multiFundingProduct.getNeedAdjust() || NeedCalculateEnum.NEED_CALCULATE.getCode().equals(multiFundingProduct.getNeedCalculate())) {
				throw new ServiceException("赎货类产品不允许融资调整和多资方融资计算");
			}
		} else {
			if (1 == multiFundingProduct.getNeedAdjust() && NeedCalculateEnum.NOT_NEED_CALCULATE.getCode().equals(multiFundingProduct.getNeedCalculate())) {
				throw new ServiceException("只有需要多资方融资计算时才能开启融资方案调整流程");
			}
		}
		Integer needCalculateOld = product.getNeedCalculate();
		// 更新产品
		MultiFundingProduct goods = BeanUtil.copyProperties(multiFundingProduct, MultiFundingProduct.class);
		updateById(goods);
		// 更新管理表
		saveOrUpdateProductManager(goods);
		ProductDTO transferProductDTO = BeanUtil.copyProperties(multiFundingProduct, ProductDTO.class);
		// 传入标签列表
        transferProductDTO.setGoodsLabelIds(multiFundingProduct.getLabelIds());
		// 设值
		saveWithProduct(transferProductDTO);
		return true;
	}

	/**
	 * 新增融资产品信息
	 * @param multiFundingProductSaveDTO 产品信息
	 * @return 融资产品id
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long saveProduct(multiFundingProductSaveAndUpdateDTO multiFundingProductSaveDTO) {
		// 检验产品信息 并保存
		checksAndSaveBaseProduct(multiFundingProductSaveDTO);
		ProductDTO productDTO = BeanUtil.copyProperties(multiFundingProductSaveDTO, ProductDTO.class);
		// 传入标签列表
		productDTO.setGoodsLabelIds(multiFundingProductSaveDTO.getLabelIds());
		// 设值
		saveWithProduct(productDTO);
		return multiFundingProductSaveDTO.getId();
	}

	/**
	 * 设置产品配置信息
	 * @param productDTO 产品信息
	 */
	void saveWithProduct(ProductDTO productDTO) {
		productConfigInfoHandlers.forEach(e -> {
			if (NEED_CONFIG.contains(e.support())) {
				e.saveWithProduct(productDTO);
			}
		});
	}

	/**
	 * 融资产品检验 保存
	 * @param multiFundingProductSaveDTO 产品信息
	 */
	@Transactional(rollbackFor = Exception.class)
	public void checksAndSaveBaseProduct(multiFundingProductSaveAndUpdateDTO multiFundingProductSaveDTO) {
		// 检查产品名称是否重复
		checkGoodsNameIsRepeat(multiFundingProductSaveDTO.getId(), multiFundingProductSaveDTO.getGoodsName());
		// 检查产品编号是否存在
		if (StringUtil.isBlank(multiFundingProductSaveDTO.getGoodsCode())) {
			// 生成一个随机产品编号 并设置到产品信息中
			multiFundingProductSaveDTO.setGoodsCode(CodeUtil.generateCode(CodeEnum.GOODS_CODE));
		}
		// 保存产品
		MultiFundingProduct goods = BeanUtil.copyProperties(multiFundingProductSaveDTO, MultiFundingProduct.class);
		saveOrUpdate(goods);
		// 产品管理保存
		multiFundingProductSaveDTO.setId(goods.getId());
		saveOrUpdateProductManager(goods);
	}

	/**
	 * 产品管理保存
	 * @param goods 融资产品信息
	 */
	private void saveOrUpdateProductManager(MultiFundingProduct goods) {
		ProductManager productManager = ProductManager.builder()
				.productId(goods.getId())
				.type(goods.getType())
				.goodsName(goods.getGoodsName())
				.capitalName("")
				.capitalLogo("")
				.build();
		productManager.setStatus(goods.getStatus());
		productManagerService.saveOrUpdateProductManager(productManager);
	}

	/**
	 * 检查产品名称是否重复
	 * @param goodsId 产品id
	 * @param goodsName 产品名称
	 */
	private void checkGoodsNameIsRepeat(Long goodsId, String goodsName) {
		LambdaQueryWrapper<MultiFundingProduct> wrapper = Wrappers.<MultiFundingProduct>lambdaQuery()
				.eq(MultiFundingProduct::getGoodsName, goodsName.trim())
				.eq(MultiFundingProduct::getStatus, GoodsEnum.ON_SHELF.getCode())
				.ne(Objects.nonNull(goodsId), MultiFundingProduct::getId, goodsId);
		Integer count = baseMapper.selectCount(wrapper);
		if (count > 0) {
			throw new ServiceException("产品名称不能重复");
		}
	}

	/**
	 * 融资端 获取多资方产品列表 分页
	 * @param query
	 * @param goodsSearchDTO
	 * @return
	 */
	@Override
	public IPage<MultiFundingProductVO> selectMultiFundingProductList(Query query, GoodsSearchDTO goodsSearchDTO) {
		IMultiFundingRiskProductService multiFundingRiskProductService = SpringUtil.getBean(IMultiFundingRiskProductService.class);
		//构造查询条件
		LambdaQueryWrapper<MultiFundingProduct> wrapper = Wrappers.<MultiFundingProduct>lambdaQuery()
				.gt(MultiFundingProduct::getRelationNum, 0)
				.eq(MultiFundingProduct::getStatus, GoodsEnum.ON_SHELF.getCode())
				.like(!StringUtil.isEmpty(goodsSearchDTO.getGoodsName()), MultiFundingProduct::getGoodsName, goodsSearchDTO.getGoodsName());
		//查询 需赎货类 产品类型
		GoodsType goodsTypeRedeem = goodsTypeService.getOne(Wrappers.<GoodsType>lambdaQuery()
				.eq(GoodsType::getBusinessType, GoodsTypeBusinessEnum.SYSTEM.getCode())
				.eq(GoodsType::getCode, GoodsTypeCodeEnum.NEED_REDEMPTION_GROUP.getCode())
		);
		if (GoodsTypeCodeEnum.NEED_REDEMPTION_GROUP.getCode().equals(goodsSearchDTO.getRedeemCode())) {
			wrapper.eq(MultiFundingProduct::getGoodsTypeId, goodsTypeRedeem.getId());
		} else {
			//查询 居间 融资产品类型
			GoodsType goodsTypeIntermediation = goodsTypeService.getOne(Wrappers.<GoodsType>lambdaQuery()
					.eq(GoodsType::getBusinessType, GoodsTypeBusinessEnum.SYSTEM.getCode())
					.eq(GoodsType::getCode, GoodsTypeCodeEnum.INTERMEDIATION.getCode())
			);
			wrapper.notIn(MultiFundingProduct::getGoodsTypeId, Arrays.asList(goodsTypeRedeem.getId(), goodsTypeIntermediation.getId()));
		}
		// 获取所有的融资产品列表
		IPage<MultiFundingProduct> page = baseMapper.selectPage(Condition.getPage(query), wrapper);
		if (ObjectUtil.isEmpty(page) || page.getTotal() <= 0) {
			return new Page<>();
		}
		IPage<MultiFundingProductVO> productVOPage = MultiFundingProductWrapper.build().pageMultiFundingProductVO(page);
		// 获取融资产品信息列表
		List<MultiFundingProductVO> groupVOList = productVOPage.getRecords();
		// 获取融资产品id列表
		List<Long> groupIdList = StreamUtil.map(groupVOList, MultiFundingProductVO::getId);

		// 获取当前用户企业类型
		Integer enterpriseType = UserUtils.getEnterpriseType();
		// 用户id
		Long userId = MyAuthUtil.getUserId();
		// 获取
		List<CustomerGoods> customerGoodsList = customerGoodsService.listCustomerGoods(userId, enterpriseType, groupIdList);
		Map<Long, CustomerGoods> customerGoodsMap = StreamUtil.toMap(customerGoodsList, CustomerGoods::getGoodsId, e -> e);
		// 不可开通列表   目前逻辑 都标识可开通 客户开通成功的就去掉标识
		List<Long> notOpenGoodsIdList = customerGoodsList.stream().map(CustomerGoods::getGroupId).collect(Collectors.toList());
		// 有记录即不可开通intermediationConsultService
		// 产品id对应对应标签列表
		Map<Long, List<GoodsLabelRelationVO>> labelMap = getMapLabelInGoodsId(groupIdList);
		// 遍历融资产品
		for (MultiFundingProductVO groupVO : groupVOList) {
			// 融资产品id
			Long id = groupVO.getId();
			// 关联资金产品列表
			List<MultiFundingAffiliatedProducts> affiliatedProducts = multiFundingAffiliatedProductsService.getAffiliatedProductIdListByGroupId(id);
			// 关联资金产品id列表
			List<Long> goodsIdList = CollStreamUtil.toList(affiliatedProducts, MultiFundingAffiliatedProducts::getGoodsId);
			// 查看客户是否在其它融资产品开通了该融资产品的关联产品 筛选出客户可开通的产品是否存在
			List<Long> canOpenGoodsIdList = multiFundingRiskProductService.probeAndScreening(goodsIdList, id, userId, enterpriseType);
			// 数据组装
			if (customerGoodsMap.containsKey(id)) {
				CustomerGoods customerGoods = customerGoodsMap.get(id);
				groupVO.setCustomerGoodsId(customerGoods.getId());
				groupVO.setEnterpriseQuotaId(customerGoods.getEnterpriseQuotaId());
				groupVO.setStatus(customerGoods.getStatus());
			}
			// 判断客户是否可开通该产品
			if (ObjectUtil.isEmpty(canOpenGoodsIdList) && ObjectUtil.isEmpty(groupVO.getCustomerGoodsId())) {
				groupVO.setHaveCustomerCanOpenProduct(false);
				notOpenGoodsIdList.add(id);
			}
			// 客户开通成功的多资方产品为false  设置可开通标识
			// 如果产品在不可开通列表里 且客户产品id不为空 则表示该产品已开通 -> false, 如果客户进行过居间咨询业务表示该产品不可咨询
			groupVO.setCanOpenStatus(notOpenGoodsIdList.contains(id) || ObjectUtil.isNotEmpty(groupVO.getCustomerGoodsId()));
			groupVO.setIsIntermediation(false);
			if (checksIsCompleted(groupVO.getId())) {
				groupVO.setIsCompleted(true);
			}
			// 组装标签数据
			if (ObjectUtil.isNotEmpty(labelMap)) {
				groupVO.setLabelList(labelMap.get(groupVO.getId()));
			}
		}
		productVOPage.setRecords(groupVOList);
		return productVOPage;
	}

	/**
	 * 获取多资方产品 对应的标签列表
	 * @param list 多资方产品id列表
	 * @return map<多资方产品id, 标签列表>
	 */
	@Override
	public Map<Long, List<GoodsLabelRelationVO>> getMapLabelInGoodsId(List<Long> list) {
		// 获取标签列表
		List<GoodsLabelRelationDTO> labelRelationList = productDirector.listConfig(list, GoodsEnum.PRODUCT_CONFIG_LABEL_RELATION.getCode(), GoodsLabelRelationDTO.class, null);
		if (CollUtil.isNotEmpty(labelRelationList)) {
			return StreamUtil.groupBy(BeanUtil.copyToList(labelRelationList, GoodsLabelRelationVO.class), GoodsLabelRelationVO::getGoodsId);
		}
		return null;
	}

	/**
	 * 融资端 获取多资方产品详情
	 * @param id 多资方产品id
	 * @param userId 用户id
	 * @return MultiFundingProductVO
	 */
	@Override
	public MultiFundingProductVO getMultiFundingProductById(Long id, Long userId) {
		multiFundingProductSaveAndUpdateDTO product = detail(id);
		ProductVO productVO = BeanUtil.copyProperties(product, ProductVO.class);
		// 有效状态
		List<Integer> validStatus = Arrays.asList(CustomerGoodsEnum.FINANCING.getCode(), CustomerGoodsEnum.EXPIRE.getCode(),
				CustomerGoodsEnum.DISABLE.getCode(), CustomerGoodsEnum.QUOTA_CHANGE.getCode());
		if (Objects.isNull(productVO)) {
			return null;
		}
		MultiFundingProductVO multiFundingProductVO = BeanUtil.copyProperties(productVO, MultiFundingProductVO.class);
		if (Objects.nonNull(userId)) {
			// 获取有效状态的客户产品信息
			List<CustomerGoods> customerGoodsList = customerGoodsService.getListByStatusAndEnterpriseIdAndGoodsId(validStatus, userId, id);
			// 设值
			if (!CollectionUtils.isEmpty(customerGoodsList)) {
				CustomerGoods customerGoods = customerGoodsList.get(0);
				multiFundingProductVO.setCustomerGoodsId(customerGoods.getId());
				multiFundingProductVO.setEnterpriseQuotaId(customerGoods.getEnterpriseQuotaId());
				multiFundingProductVO.setStatus(customerGoods.getStatus());
			}
		}
		return multiFundingProductVO;
	}

	/**
	 * 融资端 获取多资方产品列表 分页
	 *
	 * @param query
	 * @param goodsSearchDTO
	 * @return
	 */
	@Override
	public IPage<MultiFundingProductVO> getIntermediationProduct(Query query, GoodsSearchDTO goodsSearchDTO) {
		//查询 居间 融资产品类型
		GoodsType goodsType = goodsTypeService.getOne(Wrappers.<GoodsType>lambdaQuery()
				.eq(GoodsType::getBusinessType, GoodsTypeBusinessEnum.SYSTEM.getCode())
				.eq(GoodsType::getCode, GoodsTypeCodeEnum.INTERMEDIATION.getCode())
		);
		if (ObjectUtil.isEmpty(goodsType)) {
			return Condition.getPage(query);
		}
		//构造查询条件, 只查询出上架的和居间的, 剔除用户已经咨询过得
		LambdaQueryWrapper<MultiFundingProduct> queryWrapper = Wrappers.<MultiFundingProduct>lambdaQuery();
		queryWrapper.eq(MultiFundingProduct::getStatus, GoodsEnum.ON_SHELF.getCode());
		queryWrapper.eq(MultiFundingProduct::getGoodsTypeId, goodsType.getId());
		queryWrapper.like(!StringUtil.isEmpty(goodsSearchDTO.getGoodsName()), MultiFundingProduct::getGoodsName, goodsSearchDTO.getGoodsName());
		IPage<MultiFundingProduct> page = baseMapper.selectPage(Condition.getPage(query), queryWrapper);
		IPage<MultiFundingProductVO> multiFundingProductVOIPage = MultiFundingProductWrapper.build().pageMultiFundingProductVO(page);
		List<MultiFundingProductVO> records = multiFundingProductVOIPage.getRecords();
		if (CollectionUtil.isEmpty(records)) {
			return multiFundingProductVOIPage;
		}
		List<Long> groupIdList = CollStreamUtil.toList(records, MultiFundingProductVO::getId);
		// 产品id对应对应标签列表
		Map<Long, List<GoodsLabelRelationVO>> labelMap = getMapLabelInGoodsId(groupIdList);
		for (MultiFundingProductVO record : records) {
			record.setLabelList(labelMap.get(record.getId()));
		}
		return multiFundingProductVOIPage;
	}

	@Override
	public List<MultiFundingProductVO> getIntermediationProduct(GoodsSearchDTO goodsSearchDTO) {
		//查询 居间 融资产品类型
		GoodsType goodsType = goodsTypeService.getOne(Wrappers.<GoodsType>lambdaQuery()
				.eq(GoodsType::getBusinessType, GoodsTypeBusinessEnum.SYSTEM.getCode())
				.eq(GoodsType::getCode, GoodsTypeCodeEnum.INTERMEDIATION.getCode())
		);
		if (ObjectUtil.isEmpty(goodsType)) {
			return Collections.emptyList();
		}
		//构造查询条件, 只查询出上架的和居间的, 剔除用户已经咨询过得
		LambdaQueryWrapper<MultiFundingProduct> queryWrapper = Wrappers.<MultiFundingProduct>lambdaQuery();
		queryWrapper.eq(MultiFundingProduct::getStatus, GoodsEnum.ON_SHELF.getCode());
		queryWrapper.eq(MultiFundingProduct::getGoodsTypeId, goodsType.getId());
		queryWrapper.like(!StringUtil.isEmpty(goodsSearchDTO.getGoodsName()), MultiFundingProduct::getGoodsName, goodsSearchDTO.getGoodsName());
		List<MultiFundingProduct> page = baseMapper.selectList(queryWrapper);
		return MultiFundingProductWrapper.build().listVO(page);
	}

	/**
	 * 后台 分页
	 * @param query 查询条件
	 * @param multiFundingProduct 多资方产品信息
	 * @return
	 */
	@Override
	public IPage<MultiFundingProductVO> getMultiFundingProductPage(Query query, Map<String, Object> multiFundingProduct) {
		IPage<MultiFundingProductVO> iPage = leftJoinProductManager(Condition.getMPJLambdaWrapper(multiFundingProduct, MultiFundingProduct.class))
				.page(Condition.getPage(query), MultiFundingProductVO.class);
		if (iPage.getTotal() <= 0) {
			return iPage;
		}
		List<MultiFundingProductVO> records = iPage.getRecords();
		// 从产品列表中获取用户id，并查询用户
		Map<Long, String> userNameMap = UserUtils.mapUserName(StreamUtil.map(records, MultiFundingProductVO::getUpdateUser));
		// 组装数据
		records = records.stream().peek(groupVO -> {
			// 设置操作人名称
			groupVO.setOperator(userNameMap.getOrDefault(groupVO.getUpdateUser(), ""));
		}).collect(Collectors.toList());
		iPage.setRecords(records);
		return iPage;
	}

	private MPJLambdaWrapper<MultiFundingProduct> leftJoinProductManager(MPJLambdaWrapper<MultiFundingProduct> wrapper) {
		return wrapper.selectAll(MultiFundingProduct.class)
				.leftJoin(ProductManager.class, ProductManager::getProductId, MultiFundingProduct::getId)
				.selectAs(ProductManager::getCapitalLogo, Product::getCapitalLogo)
				.selectAs(ProductManager::getCapitalName, Product::getCapitalName);
	}

	/**
	 * 根据融资产品id 更新关联产品数量
	 * @param groupId 多资方产品id
	 * @param relationNum 关联产品数量
	 * @return Boolean
	 */
	@Override
	public Boolean updateRelationNumByGroupId(Long groupId, Integer relationNum) {
		return update(Wrappers.<MultiFundingProduct>lambdaUpdate()
				.set(MultiFundingProduct::getRelationNum, relationNum)
				.eq(MultiFundingProduct::getId, groupId));
	}

	/**
	 * 根据id、状态、关联产品数量 获取数量
	 * @param id id
	 * @param status 状态
	 * @param relationNum 关联产品数量
	 * @return Integer
	 */
	@Override
	public Integer getCountByIdAndStatusAndRelationNum(Long id, Integer status, Integer relationNum) {
		return count(Wrappers.<MultiFundingProduct>lambdaQuery()
				.eq(MultiFundingProduct::getId, id)
				.eq(MultiFundingProduct::getStatus, GoodsEnum.ON_SHELF.getCode())
				.gt(MultiFundingProduct::getRelationNum, 0));
	}


	@Override
	public Boolean openDirectly(Long groupId, Long userId, Integer enterpriseType) {
		MultiFundingProduct multiFundingProduct = this.getById(groupId);
		if (org.springblade.core.tool.utils.ObjectUtil.isEmpty(multiFundingProduct)){
			throw new ServiceException("该融资产品不存在!");
		}
		//保存流程
		MultiFundingProductProcessVO processVO = multiFundingProductProcessService.saveBusinessProcessProgress(groupId, MultiFundingProcessEnum.GOODS_CONFIRM_APPROVE.getCode(),
				ProcessTypeEnum.APPLY_QUOTA.getCode(), null,ProcessStatusEnum.FINISH.getCode());
		if (ObjectUtil.isEmpty(processVO)){
			throw new ServiceException("流程保存失败!");
		}
		//保存客户产品
		CustomerGoods customerGoods = CustomerGoods.builder()
				.enterpriseId(userId)
				.enterpriseType(enterpriseType)
				.goodsName(multiFundingProduct.getGoodsName())
				.goodsType(GoodsEnum.PRODUCT_GROUP_ROUTE.getCode())
				.capitalId(0L)
				.goodsId(groupId)
				.groupId(groupId)
				.openNo(CodeUtil.generateCode(CodeEnum.OPEN_NO))
				.build();
		return customerGoodsService.save(customerGoods);
	}
}
