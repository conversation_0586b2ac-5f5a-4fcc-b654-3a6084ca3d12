/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.multifunding.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.customer.dto.GoodsSearchDTO;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.multifunding.dto.ProductTemplateDTO;
import org.springblade.multifunding.dto.multiFundingProductSaveAndUpdateDTO;
import org.springblade.multifunding.vo.MultiGoodsContractTemplateVO;
import org.springblade.multifunding.vo.MyMultiFundingProductVO;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.vo.GoodsLabelRelationVO;
import org.springblade.multifunding.entity.MultiFundingProduct;
import org.springblade.multifunding.vo.MultiFundingProductVO;

import java.util.List;
import java.util.Map;

/**
 * 多资方产品表 服务类
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
public interface IMultiFundingProductService extends BaseService<MultiFundingProduct> {

	/**
	 * 获取合同模板
	 * @param productTemplateDTO
	 * @return
	 */
	List<MultiGoodsContractTemplateVO> getAvailableProductTemplate(ProductTemplateDTO productTemplateDTO);

	/**
	 * 查看我的产品-融资产品列表
	 * @param status
	 * @param enterpriseType
	 * @param userId
	 * @return
	 */
	List<MyMultiFundingProductVO> getMyMultiFundingProduct(String status, Integer enterpriseType, Long userId);

	/**
	 * 融资产品 查看详情
	 * @param groupId
	 * @return
	 */
	List<MyMultiFundingProductVO> getMyMultiFundingProductDetail(Long groupId);

	/**
	 * 查看详情
	 * @param id
	 * @return
	 */
	multiFundingProductSaveAndUpdateDTO detail(Long id);

	/**
	 * 修改融资产品信息
	 * @param multiFundingProduct
	 * @return
	 */
	Boolean updateProduct(multiFundingProductSaveAndUpdateDTO multiFundingProduct);

	/**
	 * 保存融资产品信息
	 * @param multiFundingProduct
	 * @return
	 */
	Long saveProduct(multiFundingProductSaveAndUpdateDTO multiFundingProduct);

	/**
	 * 融资端 获取多资方产品列表 分页
	 * @param query
	 * @param goodsSearchDTO
	 * @return
	 */
	IPage<MultiFundingProductVO> selectMultiFundingProductList(Query query, GoodsSearchDTO goodsSearchDTO);

	/**
	 * 融资端 获取融资咨询产品列表 分页
	 * @param query
	 * @param goodsSearchDTO
	 * @return
	 */
	IPage<MultiFundingProductVO> getIntermediationProduct(Query query, GoodsSearchDTO goodsSearchDTO);

	/**
	 * 平台端录入融资咨询需求时下拉产品
	 * @param goodsSearchDTO
	 * @return
	 */
	List<MultiFundingProductVO> getIntermediationProduct(GoodsSearchDTO goodsSearchDTO);

	/**
	 * 融资端 获取多资方产品详情
	 * @param id
	 * @param userId
	 * @return
	 */
	MultiFundingProductVO getMultiFundingProductById(Long id, Long userId);

	/**
	 * 后台
	 * 分页
	 * @param query
	 * @param multiFundingProduct
	 * @return
	 */
	IPage<MultiFundingProductVO> getMultiFundingProductPage(Query query, Map<String, Object> multiFundingProduct);

	/**
	 * 根据融资产品id 更新关联产品数量
	 * @param groupId
	 * @param relationNum
	 * @return
	 */
	Boolean updateRelationNumByGroupId(Long groupId, Integer relationNum);

	/**
	 * 根据id、状态、关联产品数量 获取数量
	 * @param id
	 * @param status
	 * @param relationNum
	 * @return
	 */
	Integer getCountByIdAndStatusAndRelationNum(Long id, Integer status, Integer relationNum);

	/**
	 * 获取多资方产品 对应的标签列表
	 * @param list
	 * @return
	 */
	Map<Long, List<GoodsLabelRelationVO>> getMapLabelInGoodsId(List<Long> list);

	/**
	 * 组装数据
	 * @param goodsId 产品id
	 * @param enterpriseQuotaId 额度id
	 * @param myMultiFundingProductVO 客户产品信息
	 * @param enterpriseQuotaMap 额度信息
	 * @param productMap 产品信息
	 * @param labelMap 标签信息
	 */
	void assemblyData(Long goodsId, Long enterpriseQuotaId, MyMultiFundingProductVO myMultiFundingProductVO,
					  Map<Long, EnterpriseQuota> enterpriseQuotaMap, Map<Long, Product> productMap, Map<Long, List<GoodsLabelRelationVO>> labelMap);

	/**
	 * 直接开通融资产品
	 */
	Boolean openDirectly(Long groupId, Long userId, Integer enterpriseType);
}
