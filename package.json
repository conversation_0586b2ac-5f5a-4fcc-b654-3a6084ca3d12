{"name": "saber-admin", "version": "2.9.0", "private": true, "scripts": {"serve": "vue-cli-service serve --max-old-space-size=1024", "serve:test": "vue-cli-service serve --mode test --max-old-space-size=1024", "build:test": "vue-cli-service build --mode test", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^1.0.2", "avue-plugin-ueditor": "^0.1.4", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "bignumber.js": "^9.1.2", "classlist-polyfill": "^1.2.0", "clipboard": "^2.0.10", "crypto-js": "^4.0.0", "dayjs": "^1.11.2", "echarts": "^5.2.2", "element-ui": "2.15.6", "js-base64": "^2.5.1", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "lodash": "^4.17.21", "mockjs": "^1.0.1-beta3", "node-gyp": "^5.0.6", "nprogress": "^0.2.0", "portfinder": "^1.0.23", "script-loader": "^0.7.2", "source-map": "^0.7.3", "umy-ui": "^1.1.6", "vform-builds": "^2.2.9", "vue": "^2.6.10", "vue-axios": "^2.1.2", "vue-cron": "^1.0.9", "vue-i18n": "^8.7.0", "vue-json-editor": "^1.4.3", "vue-okr-tree": "^1.0.10", "vue-router": "^3.0.1", "vuex": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "babel-plugin-component": "^1.1.1", "chai": "^4.1.2", "node-sass": "^6.0.1", "sass-loader": "^10.0.5", "vue-template-compiler": "^2.5.17", "webpack-bundle-analyzer": "^3.0.3"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}