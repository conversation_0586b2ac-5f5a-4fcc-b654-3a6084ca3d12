/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.plan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.Condition;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.plan.dto.CreateOrderDTO;
import org.springblade.plan.dto.TradingOrderDataQueryDTO;
import org.springblade.plan.entity.TradingOrderData;
import org.springblade.plan.service.ITradingOrderDataService;
import org.springblade.plan.service.OrderUsePlanService;
import org.springblade.plan.vo.TradingOrderDataVO;
import org.springblade.plan.wrapper.TradingOrderDataWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 交易订单数据表 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-plan/web-back/tradingOrderData")
@Api(value = "交易订单数据表", tags = "交易订单数据表接口")
public class TradingOrderDataController extends BladeController {

    private final ITradingOrderDataService tradingOrderDataService;
    private final OrderUsePlanService orderUsePlanService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入tradingOrderData")
//    @PreAuth("hasPermission('tradingOrderData:tradingOrderData:detail') or hasRole('administrator')")
    public R<TradingOrderDataVO> detail(TradingOrderData tradingOrderData) {
        TradingOrderData detail = tradingOrderDataService.getOne(Condition.getQueryWrapper(tradingOrderData));
        return R.data(TradingOrderDataWrapper.build().entityVO(detail));
    }

    /**
     * 分页 交易订单数据表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入tradingOrderData")
//    @PreAuth("hasPermission('tradingOrderData:tradingOrderData:list') or hasRole('administrator')")
    public R<IPage<TradingOrderData>> list(TradingOrderDataQueryDTO orderQueryDTO, Query query) {
        if (orderQueryDTO.getFinanceNo() != null) {
            List<TradingOrderData> data = orderUsePlanService.getOrderListByFinanceApplyNo(orderQueryDTO.getFinanceNo()).getData();
            return R.data);
        }else{
            IPage<TradingOrderData> pages = tradingOrderDataService.page(Condition.getPage(query),
                    Condition.getQueryWrapper(tradingOrderData, TradingOrderData.class).lambda().orderByDesc(TradingOrderData::getCreateTime)
            );
            return R.data(pages);
        }

    }

    /**
     * 自定义分页 交易订单数据表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入tradingOrderData")
//    @PreAuth("hasPermission('tradingOrderData:tradingOrderData:page') or hasRole('administrator')")
    public R<IPage<TradingOrderDataVO>> page(TradingOrderDataVO tradingOrderData, Query query) {
        IPage<TradingOrderData> pages = Condition.getPage(query);
        IPage<TradingOrderDataVO> pageVO = tradingOrderDataService.selectTradingOrderDataPage(pages, tradingOrderData);
        return R.data(pageVO);
    }


    /**
     * 生成订单数据
     */
    @PostMapping("/createOrder")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "生成订单数据")
//    @PreAuth("hasPermission('tradingOrderData:tradingOrderData:createOrder') or hasRole('administrator')")
    public R<TradingOrderData> createOrder(@Valid @RequestBody CreateOrderDTO orderDTO) {
        TradingOrderData orderData = tradingOrderDataService.createOrder(orderDTO);
        return R.data(orderData);
    }
    /**
     * 新增 交易订单数据表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入tradingOrderData")
//    @PreAuth("hasPermission('tradingOrderData:tradingOrderData:save') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody TradingOrderData tradingOrderData) {
        return R.status(tradingOrderDataService.save(tradingOrderData));
    }

    /**
     * 修改 交易订单数据表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入tradingOrderData")
//    @PreAuth("hasPermission('tradingOrderData:tradingOrderData:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody TradingOrderData tradingOrderData) {
        return R.status(tradingOrderDataService.updateById(tradingOrderData));
    }

    /**
     * 新增或修改 交易订单数据表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入tradingOrderData")
//    @PreAuth("hasPermission('tradingOrderData:tradingOrderData:submit') or hasRole('administrator')")
    public R<Boolean> submit(@Valid @RequestBody TradingOrderData tradingOrderData) {
        return R.status(tradingOrderDataService.saveOrUpdate(tradingOrderData));
    }

    /**
     * 删除 交易订单数据表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
//    @PreAuth("hasPermission('tradingOrderData:tradingOrderData:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(tradingOrderDataService.deleteLogic(Func.toLongList(ids)));
    }

}
