/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.plan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.plan.enums.OrderTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 测试订单数据表实体类
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
@TableName("jrzh_trading_order_data")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TradingOrderData对象", description = "交易订单数据表")
public class TradingOrderData extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;
    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;
    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    private String orderCreateTime;
    /**
     * 订单金额（元）
     */
    @ApiModelProperty(value = "订单金额（元）")
    private BigDecimal orderAmount;
    /**
     * 卖方社会统一代码
     */
    @ApiModelProperty(value = "卖方社会统一代码")
    private String salesCreditCode;
    /**
     * 卖方公司名称
     */
    @ApiModelProperty(value = "卖方公司名称")
    private String salesCompanyName;
    /**
     * 买方社会统一代码
     */
    @ApiModelProperty(value = "买方社会统一代码")
    private String buyerCreditCode;
    /**
     * 企业id
     */
    @ApiModelProperty(value = "企业id")
    private Long companyId;
    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String companyName;
    /**
     * 场景类型：1一对多，2多对一，3一对一
     * @see  org.springblade.plan.enums.TradingOrderScenarioTypeEnum
     */
    @ApiModelProperty(value = "场景类型：1一对多，2多对一，3一对一")
    private Integer scenarioType;
    /**
     * 订单来源
     * SystemTypeEnum
     */
    @ApiModelProperty(value = "订单来源")
    private Integer source;
    /**
     * 订单类型：1发票，2交割单，3合同，4采购订单，5销售订单，99默认
     *
     * @see OrderTypeEnum
     */
    @ApiModelProperty(value = "订单类型：1发票，2交割单，3合同，4采购订单，5销售订单，99默认")
    private Integer orderType;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;
    /**
     * 商品单价（元）
     */
    @ApiModelProperty(value = "商品单价（元）")
    private BigDecimal goodsPrice;
    /**
     * 定金比例（小数形式，如0.3表示30%）
     */
    @ApiModelProperty(value = "定金比例（小数形式，如0.3表示30%）")
    private BigDecimal depositRatio;
    /**
     * 计量单位
     */
    @ApiModelProperty(value="计量单位")
    private String unit;
    /**
     * 商品数量
     */
    @ApiModelProperty(value="商品数量")
    private Integer quantity;
    /**
     * 商品规格
     */
    @ApiModelProperty(value = "商品规格")
    private String spec;
    /**
     * 最迟交付日
     */
    @ApiModelProperty("最迟交付日")
    private LocalDate deliverTime;
    /**
     * 商品logo
     */
    @ApiModelProperty("商品logo")
    private String goodsLogo;
    /**
     * 实际融资需求金额
     */
    @ApiModelProperty("实际融资需求金额")
    private BigDecimal actualFinancingAmount;

}
