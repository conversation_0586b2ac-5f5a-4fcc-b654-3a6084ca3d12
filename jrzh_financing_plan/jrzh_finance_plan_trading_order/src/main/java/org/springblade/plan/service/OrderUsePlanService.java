package org.springblade.plan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.tool.api.R;
import org.springblade.plan.entity.TradingOrderData;

import java.util.List;

/**
 * 调用融资方案相关方法接口
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface OrderUsePlanService {
    /**
     * 根据融资编号查询订单列表
     */
    R<List<TradingOrderData>> getOrderListByFinanceApplyNo(String financeNo);
}
