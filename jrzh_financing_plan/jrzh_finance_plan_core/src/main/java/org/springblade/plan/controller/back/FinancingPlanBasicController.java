package org.springblade.plan.controller.back;

import cn.hutool.core.collection.CollStreamUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.Condition;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.plan.dto.FinancingPlanBasicDTO;
import org.springblade.plan.dto.PlanCalculateInfoDTO;
import org.springblade.plan.entity.FinancingPlanBasic;
import org.springblade.plan.entity.TradingOrderData;
import org.springblade.plan.enums.TradingOrderScenarioTypeEnum;
import org.springblade.plan.enums.TradingOrderStatusEnum;
import org.springblade.plan.service.IFinancingPlanBasicService;
import org.springblade.plan.service.IFinancingPlanManyToOneService;
import org.springblade.plan.service.IFinancingPlanOneToManyService;
import org.springblade.plan.service.ITradingOrderDataService;
import org.springblade.plan.vo.FinancingPlanBasicVO;
import org.springblade.plan.wrapper.FinancingPlanBasicWrapper;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 融资方案基础信息 控制器
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-plan/" + CommonConstant.WEB_BACK + "/financing-plan-basic")
@Api(value = "后台融资方案", tags = "融资方案基础信息接口")
public class FinancingPlanBasicController extends BladeController {

    private final IFinancingPlanBasicService financingPlanBasicService;
    private final IFinancingPlanOneToManyService financingPlanOneToManyService;
    private final IFinancingPlanManyToOneService financingPlanManyToOneService;
    private final ICustomerGoodsService customerGoodsService;
    private final ProductDirector productDirector;
    private final ITradingOrderDataService tradingOrderDataService;

    /**
     * 计算方案
     */
    @PostMapping("/getPlanCalculate")
    @ApiOperation(value = "计算方案")
    public R<FinancingPlanBasicDTO> getPlanCalculate(@RequestBody PlanCalculateInfoDTO planCalculateInfoDTO) {
        BigDecimal customerEnterAmount = planCalculateInfoDTO.getCustomerEnterAmount();
        CustomerGoods customerGroup = planCalculateInfoDTO.getCustomerGroup();
        List<TradingOrderData> tradingOrderDataList = planCalculateInfoDTO.getTradingOrderDataList();
        List<CustomerGoods> financingRelatedGoodsList = planCalculateInfoDTO.getFinancingRelatedGoodsList();
        Integer sortType = planCalculateInfoDTO.getSortType();
        TradingOrderData oneOrder = tradingOrderDataList.get(0);
        Long userId = customerGroup.getEnterpriseId();
        FinancingPlanBasicDTO financingPlanBasicDTO;
        if (TradingOrderScenarioTypeEnum.ONE_TO_MANY.getCode().equals(oneOrder.getScenarioType())) {
            financingPlanBasicDTO = financingPlanOneToManyService.planCalculateOneToMany(customerGroup, oneOrder, userId, financingRelatedGoodsList, sortType, customerEnterAmount);
            financingPlanBasicDTO.getFinancingPlanQuotaList().forEach(e -> {
                e.setProduct(productDirector.detail(customerGoodsService.getById(e.getCustomerGoodsId()).getGoodsId()));
            });
        } else {
            List<TradingOrderData> orderList = tradingOrderDataService.list(Wrappers.<TradingOrderData>lambdaQuery()
                    .eq(TradingOrderData::getUserId, userId)
                    .eq(TradingOrderData::getScenarioType, TradingOrderScenarioTypeEnum.MANY_TO_ONE.getCode())
                    .eq(TradingOrderData::getStatus, TradingOrderStatusEnum.ORDER_UN_USE.getCode())
                    .notIn(TradingOrderData::getId, CollStreamUtil.toList(tradingOrderDataList, TradingOrderData::getId))
            );
            orderList.addAll(tradingOrderDataList);
            financingPlanBasicDTO = financingPlanManyToOneService.planCalculateManyToOne(customerGroup, orderList, userId, financingRelatedGoodsList, sortType, customerEnterAmount);
            financingPlanBasicDTO.getFinancingPlanQuotaList().forEach(e -> {
                e.setProduct(productDirector.detail(customerGoodsService.getById(e.getCustomerGoodsId()).getGoodsId()));
            });
        }

        return R.data(financingPlanBasicDTO);
    }


    /**
     * 根据融资申请查询订单列表
     */
    @GetMapping("/getOrderListByFinanceApply")
    @ApiOperation(value = "根据融资申请查询订单列表")
    public R getOrderListByFinanceApply(@RequestParam Long financeId) {
        return financingPlanBasicService.getOrderListByFinanceApply(financeId);
    }

    /**
     * 更新所有方案
     */
    @GetMapping("/allPlanCalculate")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "更新所有方案", notes = "传入groupId")
    @PreAuth("hasPermission('financingPlan:financingPlanBasic:detail') or hasRole('administrator')")
    public R<Boolean> allGroupPlanRecalculate(@RequestParam Long userId) {
        return R.data(financingPlanBasicService.allGroupPlanRecalculate(userId));
    }


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入financingPlanBasic")
    @PreAuth("hasPermission('financingPlan:financingPlanBasic:detail') or hasRole('administrator')")
    public R<FinancingPlanBasicVO> detail(FinancingPlanBasic financingPlanBasic) {
        FinancingPlanBasic detail = financingPlanBasicService.getOne(Condition.getQueryWrapper(financingPlanBasic));
        return R.data(FinancingPlanBasicWrapper.build().entityVO(detail));
    }

    /**
     * 分页 融资方案基础表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入financingPlanBasic")
    @PreAuth("hasPermission('financingPlan:financingPlanBasic:list') or hasRole('administrator')")
    public R<IPage<FinancingPlanBasicVO>> list(FinancingPlanBasicDTO financingPlanBasic, Query query) {
        financingPlanBasic.setUserId(AuthUtil.getUserId());
        IPage<FinancingPlanBasic> pages = financingPlanBasicService.page(Condition.getPage(query), Condition.getQueryWrapper(financingPlanBasic, FinancingPlanBasic.class).lambda()
                .orderByDesc(FinancingPlanBasic::getUpdateTime));
        return R.data(FinancingPlanBasicWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 融资方案基础表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入financingPlanBasic")
    @PreAuth("hasPermission('financingPlan:financingPlanBasic:page') or hasRole('administrator')")
    public R<IPage<FinancingPlanBasicVO>> page(FinancingPlanBasicVO financingPlanBasic, Query query) {
        IPage<FinancingPlanBasicVO> pages = financingPlanBasicService.selectFinancingPlanBasicPage(Condition.getPage(query), financingPlanBasic);
        return R.data(pages);
    }

    /**
     * 新增 融资方案基础表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入financingPlanBasic")
    @PreAuth("hasPermission('financingPlan:financingPlanBasic:save') or hasRole('administrator')")
    public R<Boolean> save(@Valid @RequestBody FinancingPlanBasic financingPlanBasic) {
        return R.status(financingPlanBasicService.save(financingPlanBasic));
    }

    /**
     * 修改 融资方案基础表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入financingPlanBasic")
    @PreAuth("hasPermission('financingPlan:financingPlanBasic:update') or hasRole('administrator')")
    public R<Boolean> update(@Valid @RequestBody FinancingPlanBasic financingPlanBasic) {
        return R.status(financingPlanBasicService.updateById(financingPlanBasic));
    }

    /**
     * 新增或修改 融资方案基础表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入financingPlanBasic")
    @PreAuth("hasPermission('financingPlan:financingPlanBasic:submit') or hasRole('administrator')")
    public R<Boolean> submit(@Valid @RequestBody FinancingPlanBasic financingPlanBasic) {
        return R.status(financingPlanBasicService.saveOrUpdate(financingPlanBasic));
    }


    /**
     * 删除 融资方案基础表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @PreAuth("hasPermission('financingPlan:financingPlanBasic:remove') or hasRole('administrator')")
    public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(financingPlanBasicService.deleteLogic(Func.toLongList(ids)));
    }


}
