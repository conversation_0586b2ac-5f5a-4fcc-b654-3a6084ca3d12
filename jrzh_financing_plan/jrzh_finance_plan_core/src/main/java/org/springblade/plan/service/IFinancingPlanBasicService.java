/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.plan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.plan.dto.FinancingPlanBasicDTO;
import org.springblade.plan.dto.PlanCalculateInfoDTO;
import org.springblade.plan.dto.TradingOrderDataQueryDTO;
import org.springblade.plan.entity.FinancingPlanBasic;
import org.springblade.plan.entity.TradingOrderData;
import org.springblade.plan.vo.FinancingPlanBasicVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 融资方案基础表 服务类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface IFinancingPlanBasicService extends BaseService<FinancingPlanBasic> {

    /**
     * 自定义分页
     */
    IPage<FinancingPlanBasicVO> selectFinancingPlanBasicPage(IPage<FinancingPlanBasicVO> page, FinancingPlanBasicVO financingPlanBasic);

    /**
     * 更新所有方案
     */
    Boolean allGroupPlanRecalculate(Long userId);

    /**
     * 更新所有方案（带订单）
     */
    Boolean allGroupPlanRecalculate(Long userId, List<TradingOrderData> orderDataList, Boolean needRemoveOld);

    /**
     * 更新方案的申请状态
     */
    void updatePlanApplyStatus(Long financeId);

    /**
     * 生成水位和方案
     *
     * @param orderList    订单列表
     * @param scenarioType 交易订单场景类型  1:一对多、2：多对一
     */
    Boolean createLimitAndPlan(List<TradingOrderData> orderList, Integer scenarioType);


    /**
     * 删除订单的方案水位等数据
     *
     * @param userId
     * @param orderDataList
     */
    void removeOldData(Long userId, List<TradingOrderData> orderDataList, Integer scenarioType);

    /**
     * 生成方案     一对多
     *
     * @param customerGroup     客户融资产品
     * @param order             订单
     * @param customerGoodsList 客户可融资的融资产品关联资方产品
     * @param sortType          资方分配顺序类型
     * @return
     */
    FinancingPlanBasicDTO getPlanOneToMany(CustomerGoods customerGroup, TradingOrderData order, Long userId, List<CustomerGoods> customerGoodsList, Integer sortType);

    /**
     * 根据客户输入的金额计算方案
     *
     * @param customerEnterAmount 客户输入的金额
     * @param sortType            排序类型
     * @param planId              方案id
     * @return
     */
    FinancingPlanBasicDTO planCalculateByAmount(Long userId, BigDecimal customerEnterAmount, Long goodsId, Integer sortType, Long planId, String planNo);

    /**
     * 根据客户输入的金额计算方案-后台调整使用
     *
     * @param planCalculateInfoDTO
     * @return
     */
    FinancingPlanBasicDTO planCalculateByAmountBack(PlanCalculateInfoDTO planCalculateInfoDTO);

    /**
     * 根据用户ID和订单ID获取融资方案列表
     *
     * @param userId  用户ID
     * @param orderId 订单ID
     * @param groupId 客户融资产品ID
     * @return 融资方案列表
     */
    List<FinancingPlanBasicVO> getFinancingPlansByOrder(Long userId, Long orderId, Long groupId);

    /**
     * 根据用户ID和订单ID列表计算融资方案
     *
     * @param userId  用户ID
     * @param orderIds 订单ID列表
     * @param groupId 客户融资产品ID
     * @return 批量融资-融资方案
     */
    FinancingPlanBasicVO getFinancingPlansByOrderList(Long userId, List<Long> orderIds, Long groupId);

    /**
     * 根据融资申请查询订单列表
     * @param financeId
     * @return
     */
    R getOrderListByFinanceApply(Long financeId);

    /**
     * 获取可融资使用的订单
     * @param tradingOrderData
     * @return
     */
    List<TradingOrderData> myFinanceUseOrders(TradingOrderDataQueryDTO tradingOrderData);

}
