/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.plan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.enums.CustomerGoodsEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.GoodsTypeEnum;
import org.springblade.common.utils.Condition;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.limit.dto.InitFinancingLimitDto;
import org.springblade.finance.limit.dto.OrderDataDto;
import org.springblade.finance.limit.entity.FinancingLimit;
import org.springblade.finance.limit.enums.FinancingLimitEnum;
import org.springblade.finance.limit.service.FinancingLimitService;
import org.springblade.finance.limit.service.OrderLevelService;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.multifunding.constant.NeedCalculateEnum;
import org.springblade.multifunding.entity.MultiFundingAffiliatedProducts;
import org.springblade.multifunding.entity.MultiFundingProduct;
import org.springblade.multifunding.service.IMultiFundingAffiliatedProductsService;
import org.springblade.multifunding.service.IMultiFundingProductService;
import org.springblade.plan.config.PlanStatusMappingConfig;
import org.springblade.plan.dto.FinancingPlanBasicDTO;
import org.springblade.plan.dto.PlanCalculateInfoDTO;
import org.springblade.plan.dto.TradingOrderDataQueryDTO;
import org.springblade.plan.entity.FinancingPlanBasic;
import org.springblade.plan.entity.FinancingPlanQuota;
import org.springblade.plan.entity.FinancingPlanQuotaBind;
import org.springblade.plan.entity.TradingOrderData;
import org.springblade.plan.enums.*;
import org.springblade.plan.mapper.FinancingPlanBasicMapper;
import org.springblade.plan.service.*;
import org.springblade.plan.vo.FinancingPlanBasicVO;
import org.springblade.plan.vo.FinancingPlanQuotaVO;
import org.springblade.system.entity.Dept;
import org.springblade.system.service.IDeptService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 融资方案基础表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FinancingPlanBasicServiceImpl extends BaseServiceImpl<FinancingPlanBasicMapper, FinancingPlanBasic> implements IFinancingPlanBasicService {
    private final ICustomerGoodsService customerGoodsService;
    private final ITradingOrderDataService tradingOrderDataService;
    private final IFinancingPlanOneToManyService financingPlanOneToManyService;
    private final IFinancingPlanManyToOneService financingPlanManyToOneService;
    private final IFinancingPlanOneToOneService financingPlanOneToOneService;
    private final IFinanceApplyService financeApplyService;
    private final FinancingLimitService financingLimitService;
    private final IFinancingPlanQuotaService financingPlanQuotaService;
    private final IFinancingPlanQuotaBindService financingPlanQuotaBindService;
    private final IMultiFundingAffiliatedProductsService multiFundingAffiliatedProductsService;
    private final IDeptService deptService;
    private final OrderLevelService orderLevelService;
    private final PublicPlanService publicPlanService;
    private final IMultiFundingProductService multiFundingProductsService;


    @Override
    public IPage<FinancingPlanBasicVO> selectFinancingPlanBasicPage(IPage<FinancingPlanBasicVO> page, FinancingPlanBasicVO financingPlanBasic) {
        return page.setRecords(baseMapper.selectFinancingPlanBasicPage(page, financingPlanBasic));
    }

    /**
     * 所有资金产品组重新计算方案
     */
    @Override
    public Boolean allGroupPlanRecalculate(Long userId) {
        //查询未使用的订单，一对多
        List<TradingOrderData> orderListOneToMany = tradingOrderDataService.list(Wrappers.<TradingOrderData>lambdaQuery()
                .eq(TradingOrderData::getUserId, userId)
                .eq(TradingOrderData::getStatus, TradingOrderStatusEnum.ORDER_UN_USE)
                .eq(TradingOrderData::getScenarioType, TradingOrderScenarioTypeEnum.ONE_TO_MANY.getCode()));
        if (CollectionUtils.isNotEmpty(orderListOneToMany)) {
            this.allGroupPlanRecalculate(userId, orderListOneToMany, true);
        }

        //查询未使用的订单，多对一
        List<TradingOrderData> orderListManyToOne = tradingOrderDataService.list(Wrappers.<TradingOrderData>lambdaQuery()
                .eq(TradingOrderData::getUserId, userId)
                .eq(TradingOrderData::getStatus, TradingOrderStatusEnum.ORDER_UN_USE)
                .eq(TradingOrderData::getScenarioType, TradingOrderScenarioTypeEnum.MANY_TO_ONE.getCode())
        );
        if (CollectionUtils.isNotEmpty(orderListManyToOne)) {
            this.allGroupPlanRecalculate(userId, orderListManyToOne, true);
        }

        return true;
    }

    /**
     * 所有资金产品组重新计算方案（带订单）
     */
    @Override
    public Boolean allGroupPlanRecalculate(Long userId, List<TradingOrderData> orderDataList, Boolean needRemoveOld) {
        if (null == userId) {
            throw new ServiceException("用户ID不能为空");
        }
        Map<CustomerGoods, List<CustomerGoods>> customerGoodsMap = this.checkAndGetCustomerGoods(userId);
        Integer scenarioType = orderDataList.get(0).getScenarioType();
        //是否需要删除旧方案水位等数据
        if (needRemoveOld) {
            this.removeOldData(userId, orderDataList, scenarioType);
            this.createLimitAndPlan(orderDataList, scenarioType, customerGoodsMap, userId, true);
        } else {
            this.createLimitAndPlan(orderDataList, scenarioType, customerGoodsMap, userId, false);
        }

        //重新计算水位和方案

        //对每个资金产品组重新计算方案
/*        customerGroups.forEach(e -> {
            //查询组下可融资的产品
            List<CustomerGoods> customerGoodsUse = customerGoodsService.list(Wrappers.<CustomerGoods>lambdaQuery()
                    .eq(CustomerGoods::getEnterpriseId, userId)
                    .eq(CustomerGoods::getGroupId, e.getGroupId())
                    .eq(CustomerGoods::getStatus, CustomerGoodsEnum.FINANCING.getCode())
                    .ne(CustomerGoods::getGoodsType, GoodsEnum.PRODUCT_GROUP_ROUTE.getCode()));
            if (CollectionUtils.isEmpty(customerGoodsUse)) {
                return;
            }
            try {
                //对每个订单进行计算
                orderDataList.forEach(orderData -> {
                    OrderDataDto orderDataDto = BeanUtil.copyProperties(orderData, OrderDataDto.class);
                    orderDataDto.setOrderDate(orderData.getOrderCreateTime());
                    orderDataDto.setSellerCreditCode(orderData.getSalesCreditCode());
                    //生成水位,暂时只有不可重复使用
                    customerGoodsUse.forEach(customerGoods -> {
                        this.limitCalculate(customerGoods, orderDataDto, FinancingLimitEnum.IsReuseEnum.UN_REPEAT_USE.getCode());
                    });
                    //TODO暂时只使用最大可融资额度
                    Integer sortType = PlanCalculateSortTypeEnum.SORT_BY_MAXIMUM_LIMIT.getCode();
                    //生成方案
                    this.getPlanOneToMany(e, orderData, userId, customerGoodsUse, sortType);
                });
            } catch (Exception ex) {
                log.error("处理资金产品组" + e.getGroupId() + "失败，跳过当前组。错误原因：" + ex.getMessage());
            }

        });*/
        return true;
    }

    private Map<CustomerGoods, List<CustomerGoods>> checkAndGetCustomerGoods(Long userId) {
        //获取客户开通的融资产品组列表
        List<CustomerGoods> customerGroups = this.getCustomerGroups(userId);
        if (CollectionUtils.isEmpty(customerGroups)) {
            return null;
        }
        //再查这些融资产品组关联的产品列表
        Map<CustomerGoods, List<Long>> relatedGoods = this.getRelatedGoods(customerGroups);

        //再查客户开通了融资产品组中的哪些产品，且状态为可融资
        Map<CustomerGoods, List<CustomerGoods>> customerGoodsMap = this.getCustomerGoodsMap(relatedGoods, userId);
        if (customerGoodsMap.values().stream().allMatch(CollectionUtils::isEmpty)) {
            return null;
        }
        return customerGoodsMap;
    }


    /**
     * 更新方案的申请状态
     */
    @Override
    public void updatePlanApplyStatus(Long financeId) {
        FinancingPlanQuota planQuota = financingPlanQuotaService.getOne(Wrappers.<FinancingPlanQuota>lambdaQuery().eq(FinancingPlanQuota::getFinanceId, financeId));
        if (ObjectUtil.isEmpty(planQuota)) {
            return;
        }
        FinancingPlanBasic planBasic = this.getOne(Wrappers.<FinancingPlanBasic>lambdaQuery().eq(FinancingPlanBasic::getPlanNo, planQuota.getPlanNo()));
        List<Long> financeIds = financingPlanQuotaService.getByPlanNo(planBasic.getPlanNo()).stream().map(FinancingPlanQuota::getFinanceId).collect(Collectors.toList());
        List<Integer> financeStatusList = financeApplyService.listByIds(financeIds).stream().map(FinanceApply::getStatus).collect(Collectors.toList());

        // 使用配置化的状态映射来确定最终状态
        Integer finalStatus = PlanStatusMappingConfig.calculatePlanApplyStatus(financeStatusList);
        planBasic.setApplyStatus(finalStatus);
        this.updateById(planBasic);
    }

    /**
     * 生成水位和方案
     */
    @Override
    public Boolean createLimitAndPlan(List<TradingOrderData> orderList, Integer scenarioType) {
        if (CollectionUtils.isEmpty(orderList)) {
            return false;
        }
        Long userId = orderList.get(0).getUserId();
        //获取客户融资产品，及其对应的可融资的资方产品列表
        Map<CustomerGoods, List<CustomerGoods>> customerGoodsMap = this.checkAndGetCustomerGoods(userId);
        //创建水位和方案
        return this.createLimitAndPlan(orderList, scenarioType, customerGoodsMap, userId, true);
    }

    /**
     * 生成水位和方案
     */
    public Boolean createLimitAndPlan(List<TradingOrderData> orderList, Integer scenarioType, Map<CustomerGoods, List<CustomerGoods>> customerGoodsMap, Long userId, Boolean needCreateLimit) {
        if (null == customerGoodsMap) {
            return false;
        }
        List<CustomerGoods> customerGoodsListDistinct = customerGoodsMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());

        if (TradingOrderScenarioTypeEnum.ONE_TO_MANY.getCode().equals(scenarioType)) {
            orderList.forEach(order -> {
                OrderDataDto orderDataDto = BeanUtil.copyProperties(order, OrderDataDto.class);
                orderDataDto.setOrderDate(order.getOrderCreateTime());
                orderDataDto.setSellerCreditCode(order.getSalesCreditCode());
                orderDataDto.setOrderAmount(null == order.getActualFinancingAmount() ? order.getOrderAmount() : order.getActualFinancingAmount());
                //对订单生成水位
                if (needCreateLimit) {
                    customerGoodsListDistinct.forEach(goods -> {
                        this.limitCalculate(goods, Collections.singletonList(orderDataDto), FinancingLimitEnum.IsReuseEnum.UN_REPEAT_USE.getCode());
                    });
                }
                //TODO暂时默认生成最大可融资额度的方案
                Integer sortType = PlanCalculateSortTypeEnum.SORT_BY_MAXIMUM_LIMIT.getCode();
                //对客户所有融资产品组都生成订单的融资方案
                customerGoodsMap.forEach((customerGroup, customerGoodsList) -> {
                    try {
                        //查询融资产品
                        MultiFundingProduct multiFundingProduct = multiFundingProductsService.getById(customerGroup.getGroupId());
                        if (NeedCalculateEnum.NOT_NEED_CALCULATE.getCode().equals(multiFundingProduct.getNeedCalculate())) {
                            //不需要配资计算
                            this.getPlanOneToOne(customerGroup, order, userId, customerGoodsList);
                        } else {
                            //需要配资计算
                            this.getPlanOneToMany(customerGroup, order, userId, customerGoodsList, sortType);
                        }
                    } catch (Exception ex) {
                        log.error("计算方案异常：资金产品组" + customerGroup.getGoodsName() + "，跳过当前组。错误原因：" + ex.getMessage());
                    }
                });


            });
        } else {
            TradingOrderData order = orderList.get(0);
            OrderDataDto orderDataDto = new OrderDataDto();
            orderDataDto.setOrderDate(order.getOrderCreateTime());
            orderDataDto.setSellerCreditCode(order.getSalesCreditCode());
            orderDataDto.setBuyerCreditCode(order.getBuyerCreditCode());
            orderDataDto.setCompanyId(order.getCompanyId());
            orderDataDto.setCompanyName(order.getCompanyName());
            orderDataDto.setOrderAmount(orderList.stream().map(TradingOrderData::getActualFinancingAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            orderDataDto.setOrderNo("");
            //对订单生成水位
            if (needCreateLimit) {
                customerGoodsListDistinct.forEach(goods -> {
                    this.limitCalculate(goods, Collections.singletonList(orderDataDto), FinancingLimitEnum.IsReuseEnum.UN_REPEAT_USE.getCode());
                });
            }
            //TODO暂时默认生成最大可融资额度的方案
            Integer sortType = PlanCalculateSortTypeEnum.SORT_BY_MAXIMUM_LIMIT.getCode();
            //对客户所有融资产品组都生成订单列表的融资方案
            customerGoodsMap.forEach((customerGroup, customerGoodsList) -> {
                try {
                    //查询融资产品
                    MultiFundingProduct multiFundingProduct = multiFundingProductsService.getById(customerGroup.getGroupId());
                    if (NeedCalculateEnum.NOT_NEED_CALCULATE.getCode().equals(multiFundingProduct.getNeedCalculate())) {
                        //不需要配资计算
                        this.getPlanOneToOne(customerGroup, order, userId, customerGoodsList);
                    } else {
                        //需要配资计算
                        this.getPlanManyToOne(customerGroup, orderList, userId, customerGoodsList, sortType);
                    }

                } catch (Exception ex) {
                    log.error("计算方案异常：资金产品组" + customerGroup.getGoodsName() + "，跳过当前组。错误原因：" + ex.getMessage());
                }
            });
        }
        return true;
    }


    private List<CustomerGoods> getCustomerGroups(Long userId) {
        return customerGoodsService.list(Wrappers.<CustomerGoods>lambdaQuery()
                .eq(CustomerGoods::getEnterpriseId, userId)
                .eq(CustomerGoods::getGoodsType, GoodsEnum.PRODUCT_GROUP_ROUTE.getCode())
                .apply("goods_id=group_id")
        );
    }

    private Map<CustomerGoods, List<CustomerGoods>> getCustomerGoodsMap(Map<CustomerGoods, List<Long>> relatedGoods, Long userId) {
        Map<CustomerGoods, List<CustomerGoods>> customerGoodsMap = new HashMap<>();
        relatedGoods.forEach((customerGroup, relatedGoodsIds) -> {
            try {
                List<CustomerGoods> cGoods = customerGoodsService.list(Wrappers.<CustomerGoods>lambdaQuery()
                        .eq(CustomerGoods::getEnterpriseId, userId)
                        .eq(CustomerGoods::getStatus, CustomerGoodsEnum.FINANCING.getCode())
                        .in(CustomerGoods::getGoodsId, relatedGoodsIds));
                customerGoodsMap.put(customerGroup, cGoods);
            } catch (Exception ex) {
                log.error("处理资金产品组" + customerGroup.getGoodsName() + "失败，跳过当前组。错误原因：" + ex.getMessage());
            }
        });
        return customerGoodsMap;
    }

    private Map<CustomerGoods, List<Long>> getRelatedGoods(List<CustomerGoods> customerGroups) {
        Map<CustomerGoods, List<Long>> relatedGoods = new HashMap<>();
        customerGroups.forEach(item -> {
            List<MultiFundingAffiliatedProducts> list1 = multiFundingAffiliatedProductsService
                    .list(Wrappers.<MultiFundingAffiliatedProducts>lambdaQuery()
                            .eq(MultiFundingAffiliatedProducts::getGroupId, item.getGoodsId()));
            relatedGoods.put(item, CollStreamUtil.toList(list1, MultiFundingAffiliatedProducts::getGoodsId));
        });
        return relatedGoods;
    }

    @Override
    public void removeOldData(Long userId, List<TradingOrderData> orderDataList, Integer scenarioType) {
        if (TradingOrderScenarioTypeEnum.ONE_TO_MANY.getCode().equals(scenarioType)) {
            //删除原有未使用的方案
            List<FinancingPlanBasic> basicList = this.list(Wrappers.<FinancingPlanBasic>lambdaQuery()
                    .eq(FinancingPlanBasic::getUserId, userId)
                    .in(FinancingPlanBasic::getScenarioType, Arrays.asList(TradingOrderScenarioTypeEnum.ONE_TO_MANY.getCode(), TradingOrderScenarioTypeEnum.ONE_TO_ONE.getCode()))
                    .eq(FinancingPlanBasic::getStatus, FinancingPlanBasicStatusEnum.PLAN_UN_USE.getCode()));
            removePlan(orderDataList, basicList);
            //删除订单原水位
            orderDataList.forEach(orderData -> {
                financingLimitService.remove(Wrappers.<FinancingLimit>lambdaQuery().eq(FinancingLimit::getOrderNo, orderData.getOrderNo()));
            });
        } else {
            //删除原有未使用的方案
            List<FinancingPlanBasic> basicList = this.list(Wrappers.<FinancingPlanBasic>lambdaQuery()
                    .eq(FinancingPlanBasic::getUserId, userId)
                    .eq(FinancingPlanBasic::getScenarioType, TradingOrderScenarioTypeEnum.MANY_TO_ONE.getCode())
                    .eq(FinancingPlanBasic::getStatus, FinancingPlanBasicStatusEnum.PLAN_UN_USE.getCode()));
            removePlan(orderDataList, basicList);
            //删除水位
            orderDataList.forEach(orderData -> {
                financingLimitService.remove(Wrappers.<FinancingLimit>lambdaQuery()
                        .eq(FinancingLimit::getOrderNo, "")
                        .eq(FinancingLimit::getCompanyId, userId)
                        .eq(FinancingLimit::getStatus, FinancingLimitEnum.StatusEnum.NOT_USED.getCode())
                );
            });
        }

    }

    private void removePlan(List<TradingOrderData> orderDataList, List<FinancingPlanBasic> basicList) {
        if (CollectionUtils.isNotEmpty(basicList)) {
            //删除方案基础数据
            this.removeByIds(CollStreamUtil.toList(basicList, FinancingPlanBasic::getId));
            //删除方案配额数据
            List<FinancingPlanQuota> planQuotas = financingPlanQuotaService.list(Wrappers.<FinancingPlanQuota>lambdaQuery().in(FinancingPlanQuota::getPlanNo, CollStreamUtil.toList(basicList, FinancingPlanBasic::getPlanNo)));
            List<Long> planQuotaIds = CollStreamUtil.toList(planQuotas, FinancingPlanQuota::getId);
            financingPlanQuotaService.removeByIds(planQuotaIds);
            //删除配额绑定数据
            financingPlanQuotaBindService.remove(Wrappers.<FinancingPlanQuotaBind>lambdaQuery().in(FinancingPlanQuotaBind::getFinancingQuotaId, planQuotaIds));
        }
    }

    public void limitCalculate(CustomerGoods customerGoods, List<OrderDataDto> orderDataDtos, String isReuse) {
        if (cn.hutool.core.util.ObjectUtil.isEmpty(customerGoods)) {
            throw new ServiceException("开通的产品不存在");
        }
        Long goodsId = customerGoods.getGoodsId();
        Long capitalId = customerGoods.getCapitalId();
        Dept dept = deptService.getById(capitalId);
        if (cn.hutool.core.util.ObjectUtil.isEmpty(dept)) {
            throw new ServiceException("资金方不存在");
        }
        InitFinancingLimitDto initDto = new InitFinancingLimitDto();
        initDto.setDept(dept);
        initDto.setGoodsId(goodsId);
//        initDto.setOrderDataDtoList(Collections.singletonList(orderDataDto));
        initDto.setOrderDataDtoList(orderDataDtos);
        initDto.setIsReuse(isReuse);
        initDto.setEnterpriseId(customerGoods.getEnterpriseId());
        initDto.setEnterpriseType(customerGoods.getEnterpriseType());
        orderLevelService.initFinancingLimitByJob(initDto, GoodsTypeEnum.ORDER_FINANCING.getCode(), FinancingLimitEnum.MySqlDateFormatEnum.DAY_LE);
    }


    /**
     * 生成方案、配额、配额绑定订单数据     一对多
     */
    @Override
    public FinancingPlanBasicDTO getPlanOneToMany(CustomerGoods customerGroup, TradingOrderData order, Long userId, List<CustomerGoods> customerGoodsList, Integer sortType) {
        //计算方案
        FinancingPlanBasicDTO financingPlanBasicDTO = financingPlanOneToManyService.planCalculateOneToMany(customerGroup, order, userId, customerGoodsList, sortType, null);
        FinancingPlanBasic financingPlanBasic = BeanUtil.copyProperties(financingPlanBasicDTO, FinancingPlanBasic.class);
        //保存配额
        List<FinancingPlanQuotaVO> financingPlanQuotaVos = financingPlanBasicDTO.getFinancingPlanQuotaList();
        List<FinancingPlanQuota> quotas = CollStreamUtil.toList(financingPlanQuotaVos, e -> BeanUtil.copyProperties(e, FinancingPlanQuota.class));
        financingPlanQuotaService.saveBatch(quotas);
        //保存配额绑定
        List<FinancingPlanQuotaBind> binds = CollStreamUtil.toList(quotas, e -> publicPlanService.buildBind(e.getId(), order.getId()));
        financingPlanQuotaBindService.saveBatch(binds);
        //保存方案
        this.save(financingPlanBasic);
        financingPlanBasicDTO.setFinancingPlanQuotaBindList(binds);
        return financingPlanBasicDTO;
    }

    /**
     * 生成方案、配额、配额绑定订单数据     一对一
     */
    private List<FinancingPlanBasicDTO> getPlanOneToOne(CustomerGoods customerGroup, TradingOrderData order, Long userId, List<CustomerGoods> customerGoodsList) {
        // 使用ROUND_HALF_UP避免除法运算的ArithmeticException，保留2位小数
        List<FinancingPlanBasicDTO> financingPlanBasicDtos = financingPlanOneToOneService.planCalculateOneToOne(customerGroup, order, userId, customerGoodsList, null);
        for (FinancingPlanBasicDTO financingPlanBasicDTO : financingPlanBasicDtos) {
            //融资方案
            FinancingPlanBasic financingPlanBasic = BeanUtil.copyProperties(financingPlanBasicDTO, FinancingPlanBasic.class);
            this.save(financingPlanBasic);
            //配额
            List<FinancingPlanQuotaVO> financingPlanQuotaVos = financingPlanBasicDTO.getFinancingPlanQuotaList();
            FinancingPlanQuota quota = BeanUtil.copyProperties(financingPlanQuotaVos.get(0), FinancingPlanQuota.class);
            financingPlanQuotaService.save(quota);
            //配额与订单绑定
            FinancingPlanQuotaBind bind = publicPlanService.buildBind(quota.getId(), order.getId());
            financingPlanQuotaBindService.save(bind);
        }
        return financingPlanBasicDtos;
    }

    /**
     * 生成方案、配额数据     多对一
     */
//    @Override
    public FinancingPlanBasicDTO getPlanManyToOne(CustomerGoods customerGroup, List<TradingOrderData> orderList, Long userId, List<CustomerGoods> customerGoodsList, Integer sortType) {
        //计算方案
        List<TradingOrderData> orderListCopy = new ArrayList<>(orderList);
        FinancingPlanBasicDTO financingPlanBasicDTO = financingPlanManyToOneService.planCalculateManyToOne(customerGroup, orderListCopy, userId, customerGoodsList, sortType, null);
        FinancingPlanBasic financingPlanBasic = BeanUtil.copyProperties(financingPlanBasicDTO, FinancingPlanBasic.class);
        //保存配额
        List<FinancingPlanQuotaVO> financingPlanQuotaVos = financingPlanBasicDTO.getFinancingPlanQuotaList();
        financingPlanQuotaVos.forEach(e -> {
            FinancingPlanQuota quota = BeanUtil.copyProperties(e, FinancingPlanQuota.class);
            financingPlanQuotaService.save(quota);
            e.getFinancingPlanQuotaBindList().forEach(bind -> {
                bind.setFinancingQuotaId(quota.getId());
            });
            financingPlanQuotaBindService.saveBatch(e.getFinancingPlanQuotaBindList());

        });
//        List<FinancingPlanQuota> quotas = CollStreamUtil.toList(financingPlanQuotaVos, e -> BeanUtil.copyProperties(e, FinancingPlanQuota.class));
//        financingPlanQuotaService.saveBatch(quotas);
        //删除未使用的旧方案
        this.remove(Wrappers.<FinancingPlanBasic>lambdaQuery()
                .eq(FinancingPlanBasic::getUserId, userId)
                .eq(FinancingPlanBasic::getGroupId, customerGroup.getGoodsId())
                .eq(FinancingPlanBasic::getStatus, FinancingPlanBasicStatusEnum.PLAN_UN_USE.getCode())
                .eq(FinancingPlanBasic::getScenarioType, TradingOrderScenarioTypeEnum.MANY_TO_ONE.getCode())
        );
        //保存方案
        this.save(financingPlanBasic);

        return financingPlanBasicDTO;
    }

    /**
     * 根据客户输入的金额计算方案
     *
     * @param customerEnterAmount 客户输入的金额
     * @param sortType            排序类型
     * @param planId              方案id
     * @return
     */
    @Override
    public FinancingPlanBasicDTO planCalculateByAmount(Long userId, BigDecimal customerEnterAmount, Long goodsId, Integer sortType, Long planId, String planNo) {
        FinancingPlanBasic planBasic = this.getById(planId);
        List<FinancingPlanQuota> quotas = financingPlanQuotaService.list(Wrappers.<FinancingPlanQuota>lambdaQuery()
                .eq(FinancingPlanQuota::getPlanNo, planNo));
        if (ObjectUtil.isEmpty(planBasic)) {
            throw new ServiceException("方案不存在");
        }
        if (TradingOrderScenarioTypeEnum.ONE_TO_ONE.getCode().equals(planBasic.getScenarioType())) {
            return financingPlanOneToOneService.planCalculateOneToOneByAmount(userId, customerEnterAmount, planBasic, quotas);
        }

        //获取客户融资产品，及其对应的可融资的资方产品列表
        Map<CustomerGoods, List<CustomerGoods>> customerGoodsMap = this.checkAndGetCustomerGoods(userId);
        if (null == customerGoodsMap) {
            return null;
        }
        CustomerGoods customerGroup = null;
        List<CustomerGoods> customerGoodsList = null;
        for (Map.Entry<CustomerGoods, List<CustomerGoods>> entry : customerGoodsMap.entrySet()) {
            if (entry.getKey().getGroupId().equals(goodsId)) {
                customerGroup = entry.getKey();
                customerGoodsList = entry.getValue();
                break;
            }
        }
        if (ObjectUtil.isEmpty(customerGroup) || CollectionUtils.isEmpty(customerGoodsList)) {
            return null;
        }

        List<FinancingPlanQuotaBind> binds = financingPlanQuotaBindService.list(Wrappers.<FinancingPlanQuotaBind>lambdaQuery()
                .in(FinancingPlanQuotaBind::getFinancingQuotaId, CollStreamUtil.toList(quotas, FinancingPlanQuota::getId)));
        List<Long> orderIds = CollStreamUtil.toList(binds, FinancingPlanQuotaBind::getOrderId);
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new ServiceException("当前无订单可使用");
        }
        List<TradingOrderData> orderList = tradingOrderDataService.list(Wrappers.<TradingOrderData>lambdaQuery()
                .in(TradingOrderData::getId, orderIds));
        if (TradingOrderScenarioTypeEnum.MANY_TO_ONE.getCode().equals(orderList.get(0).getScenarioType())) {
            //取已绑定的订单和未使用的订单
            List<TradingOrderData> orderList1 = tradingOrderDataService.list(Wrappers.<TradingOrderData>lambdaQuery()
                    .eq(TradingOrderData::getCompanyId, customerGroup.getEnterpriseId())
                    .eq(TradingOrderData::getScenarioType, TradingOrderScenarioTypeEnum.MANY_TO_ONE.getCode())
                    .eq(TradingOrderData::getStatus, TradingOrderStatusEnum.ORDER_UN_USE)
                    .notIn(TradingOrderData::getId, orderIds)
            );
            orderList1.addAll(orderList);
            return financingPlanManyToOneService.planCalculateManyToOneByAmount(orderList1, customerGoodsList, sortType, customerEnterAmount, planId, quotas, null);
        } else {
            return financingPlanOneToManyService.planCalculateOneToManyByAmount(quotas, customerEnterAmount);
        }

    }

    /**
     * 根据客户输入的金额计算方案-后台调整使用
     *
     * @param planCalculateInfoDTO
     * @return
     */
    @Override
    public FinancingPlanBasicDTO planCalculateByAmountBack(PlanCalculateInfoDTO planCalculateInfoDTO) {

/*        CustomerGoods customerGroup = planCalculateInfoDTO.getCustomerGroup();
        List<TradingOrderData> orderList = planCalculateInfoDTO.getTradingOrderDataList();
        List<CustomerGoods> customerGoodsList = planCalculateInfoDTO.getFinancingRelatedGoodsList();
        Integer sortType = planCalculateInfoDTO.getSortType();
        Integer scenarioType = orderList.get(0).getScenarioType();
        BigDecimal customerEnterAmount = planCalculateInfoDTO.getCustomerEnterAmount();
        Long userId = customerGroup.getEnterpriseId();

        if (TradingOrderScenarioTypeEnum.ONE_TO_MANY.getCode().equals(scenarioType)) {
            return financingPlanOneToManyService.planCalculateOneToManyByAmount(customerGroup, orderList.get(0), userId, customerGoodsList, sortType, customerEnterAmount);
        } else {
            return financingPlanManyToOneService.planCalculateManyToOneByAmount(orderList,customerGoodsList, sortType, customerEnterAmount, null, null,null);
        }*/
        return null;
    }

    @Override
    public List<FinancingPlanBasicVO> getFinancingPlansByOrder(Long userId, Long orderId, Long groupId) {
        return getFinancingPlansByOrderInternal(userId, orderId, groupId, true);
    }

    /**
     * 内部方法：根据订单查询融资方案
     *
     * @param userId  用户ID
     * @param orderId 订单ID
     * @param groupId 产品组ID
     * @param isRetry 是否需要重试调用
     * @return 融资方案列表
     */
    private List<FinancingPlanBasicVO> getFinancingPlansByOrderInternal(Long userId, Long orderId, Long groupId, boolean isRetry) {
        try {
            MultiFundingProduct multiFundingProduct = multiFundingProductsService.getById(groupId);

            Integer scenarioType = NeedCalculateEnum.NOT_NEED_CALCULATE.getCode().equals(multiFundingProduct.getNeedCalculate()) ?
                    TradingOrderScenarioTypeEnum.ONE_TO_ONE.getCode() : TradingOrderScenarioTypeEnum.ONE_TO_MANY.getCode();
            // 1. 根据orderId查询配额绑定关系，获取financingQuotaId列表
            List<FinancingPlanQuotaBind> quotaBinds = financingPlanQuotaBindService.list(
                    Wrappers.<FinancingPlanQuotaBind>lambdaQuery()
                            .eq(FinancingPlanQuotaBind::getOrderId, orderId)
            );

            if (CollectionUtils.isEmpty(quotaBinds)) {
                throw new ServiceException(FinancingPlanExceptionEnum.NO_FINANCING_PLAN_FOUND);
            }

            // 2. 根据financingQuotaId列表查询配额信息，获取planNo列表
            List<Long> financingQuotaIds = CollStreamUtil.toList(quotaBinds, FinancingPlanQuotaBind::getFinancingQuotaId);
            List<FinancingPlanQuota> quotas = financingPlanQuotaService.list(
                    Wrappers.<FinancingPlanQuota>lambdaQuery()
                            .in(FinancingPlanQuota::getId, financingQuotaIds)
            );

            if (CollectionUtils.isEmpty(quotas)) {
                throw new ServiceException(FinancingPlanExceptionEnum.NO_FINANCING_PLAN_FOUND);
            }

            // 3. 根据planNo列表和userId查询融资方案
            List<String> planNos = CollStreamUtil.toList(quotas, FinancingPlanQuota::getPlanNo);
            List<FinancingPlanBasic> financingPlans = this.list(
                    Wrappers.<FinancingPlanBasic>lambdaQuery()
                            .eq(FinancingPlanBasic::getUserId, userId)
                            .eq(FinancingPlanBasic::getGroupId, groupId)
                            .eq(FinancingPlanBasic::getScenarioType, scenarioType)
                            .in(FinancingPlanBasic::getPlanNo, planNos)
                            .orderByDesc(FinancingPlanBasic::getUpdateTime)
            );
            //TODO判断是否是已经正在使用融资方案，即点击上一步回到的页面，不进行多资方计算情况下，目前只展示已选择的方案，无法选择其他方案
            List<FinancingPlanBasic> filter = StreamUtil.filter(financingPlans, e -> FinancingPlanBasicStatusEnum.PLAN_IN_USE.getCode().equals(e.getStatus()));
            if (CollectionUtils.isNotEmpty(filter)) {
                return BeanUtil.copyToList(filter, FinancingPlanBasicVO.class);
            }
            // 4. 转换为VO并返回
            return BeanUtil.copyToList(financingPlans, FinancingPlanBasicVO.class);

        } catch (ServiceException e) {
            // 如果是"未查询到订单对应的融资方案"异常且不是重试调用，则尝试重新计算方案
            if (FinancingPlanExceptionEnum.NO_FINANCING_PLAN_FOUND.getCode() == (e.getResultCode().getCode()) && isRetry) {
                log.info("未查询到订单对应的融资方案，尝试重新计算方案。userId: {}, orderId: {}, groupId: {}", userId, orderId, groupId);
                // 查询订单数据
                TradingOrderData orderData = tradingOrderDataService.getById(orderId);
                if (orderData == null) {
                    throw new ServiceException(FinancingPlanExceptionEnum.ORDER_NOT_FOUND);
                }

                // 执行重新计算方案
                List<TradingOrderData> orderDataList = Collections.singletonList(orderData);
                Boolean recalculateResult = this.allGroupPlanRecalculate(userId, orderDataList, true);

                if (Boolean.TRUE.equals(recalculateResult)) {
                    log.info("方案重新计算成功，再次查询融资方案。userId: {}, orderId: {}, groupId: {}", userId, orderId, groupId);
                    // 重新计算成功后，再次查询融资方案（标记为重试调用，避免无限循环）
                    return getFinancingPlansByOrderInternal(userId, orderId, groupId, false);
                } else {
                    log.error("方案重新计算失败。userId: {}, orderId: {}, groupId: {}", userId, orderId, groupId);
                    throw new ServiceException(FinancingPlanExceptionEnum.PLAN_RECALCULATE_FAILED);
                }
            } else {
                // 如果是重试调用或其他异常，直接抛出
                throw e;
            }
        }
    }

    /**
     * 根据融资申请查询订单列表
     */
    @Override
    public R getOrderListByFinanceApply(Long financeId) {
        //融资配额
        FinancingPlanQuota quota = financingPlanQuotaService.getOne(Wrappers.<FinancingPlanQuota>lambdaQuery().eq(FinancingPlanQuota::getFinanceId, financeId));
        if (ObjectUtil.isEmpty(quota)) {
            return R.fail("融资配额不存在");
        }
        //融资配额绑定订单列表
        List<FinancingPlanQuotaBind> binds = financingPlanQuotaBindService.list(Wrappers.<FinancingPlanQuotaBind>lambdaQuery().eq(FinancingPlanQuotaBind::getFinancingQuotaId, quota.getId()));
        if (CollUtil.isEmpty(binds)) {
            return R.fail("交易订单数据为空");
        }
        List<Long> orderIds = CollStreamUtil.toList(binds, FinancingPlanQuotaBind::getOrderId);
        List<TradingOrderData> tradingOrderDataList = tradingOrderDataService.listByIds(orderIds);
        if (CollUtil.isEmpty(tradingOrderDataList)) {
            return R.fail("交易订单数据为空");
        }
        return R.data(tradingOrderDataList);
    }


    @Override
    public FinancingPlanBasicVO getFinancingPlansByOrderList(Long userId, List<Long> orderIds, Long groupId) {
        Integer manyToOneCode = TradingOrderScenarioTypeEnum.MANY_TO_ONE.getCode();
        //需要计算的订单列表
        List<TradingOrderData> orderList = tradingOrderDataService.listByIds(orderIds);

        //客户开通的融资产品
        CustomerGoods group = customerGoodsService.getOne(Wrappers.<CustomerGoods>lambdaQuery()
                .eq(CustomerGoods::getEnterpriseId, userId)
                .eq(CustomerGoods::getGoodsType, GoodsEnum.PRODUCT_GROUP_ROUTE.getCode())
                .eq(CustomerGoods::getGroupId, groupId)
                .apply("goods_id=group_id"));

        //融资产品关联的所有资金产品
        Map<CustomerGoods, List<Long>> relatedGoods = this.getRelatedGoods(Collections.singletonList(group));

        //客户已开通的可融资的资金产品
        Map<CustomerGoods, List<CustomerGoods>> customerGoodsMap = this.getCustomerGoodsMap(relatedGoods, userId);
        if (customerGoodsMap.values().stream().allMatch(CollectionUtils::isEmpty)) {
            throw new ServiceException("客户未开通资方产品");
        }

        //删除该客户融资产品原有的未使用的多对一方案
        this.removeOldData(userId, orderList, manyToOneCode);

        //创建方案
        Boolean limitAndPlan = this.createLimitAndPlan(orderList, manyToOneCode, customerGoodsMap, userId, true);

        if (!limitAndPlan) {
            throw new ServiceException("计算批量融资方案失败");
        }
        FinancingPlanBasic plan = this.getOne(Wrappers.<FinancingPlanBasic>lambdaQuery()
                .eq(FinancingPlanBasic::getUserId, userId)
                .eq(FinancingPlanBasic::getGroupId, groupId)
                .eq(FinancingPlanBasic::getScenarioType, manyToOneCode)
                .eq(FinancingPlanBasic::getStatus, FinancingPlanBasicStatusEnum.PLAN_UN_USE.getCode())
                .orderByDesc(FinancingPlanBasic::getCreateTime)
                .last("limit 1")
        );
        return BeanUtil.copyProperties(plan, FinancingPlanBasicVO.class);
    }

    /**
     * 获取可融资使用的订单
     * @param tradingOrderData
     * @return
     */
    @Override
    public List<TradingOrderData> myFinanceUseOrders(TradingOrderDataQueryDTO tradingOrderData) {
        Map<String, Object> query = new HashMap<>();
        query.put("statusEqual", tradingOrderData.getStatus());
        query.put("userIdEqual", tradingOrderData.getUserId());

        return Condition.getMPJLambdaWrapper(query, TradingOrderData.class)
                .selectAll(TradingOrderData.class)
                .leftJoin(FinancingPlanQuotaBind.class, FinancingPlanQuotaBind::getOrderId, TradingOrderData::getId)
                .leftJoin(FinancingPlanQuota.class, FinancingPlanQuota::getId, FinancingPlanQuotaBind::getFinancingQuotaId)
                .leftJoin(FinancingPlanBasic.class, FinancingPlanBasic::getPlanNo, FinancingPlanQuota::getPlanNo)
                .list(TradingOrderData.class)
                ;
    }


}
