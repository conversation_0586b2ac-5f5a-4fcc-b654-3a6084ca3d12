package org.springblade.plan.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.plan.entity.TradingOrderData;
import org.springblade.plan.service.IFinancingPlanBasicService;
import org.springblade.plan.service.OrderUsePlanService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 调用融资方案相关方法实现类
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderUsePlanServiceImpl implements OrderUsePlanService {

    private final IFinanceApplyService financeApplyService;
    private final IFinancingPlanBasicService financingPlanBasicService;


    @Override
    public R<List<TradingOrderData>> getOrderListByFinanceApplyNo(String financeNo) {
        FinanceApply financeApply = financeApplyService.getByFinanceNo(financeNo);
        return financingPlanBasicService.getOrderListByFinanceApply(financeApply.getId());
    }
}
